export interface MetafieldInput {
  ownerId: string;
  namespace: string;
  type: string;
  key: string;
  value: string | number | null;
}

export interface MetafieldDefinitionCreateInput {
  name: string;
  namespace: string;
  key: string;
  type: string;
  ownerType: string;
  validations: {
    name: string;
    value: string | null;
  };
}

export interface MetafieldDefinitionCreateResponse {
  id: string;
  name: string;
}

export interface Metafield {
  key: string;
  value: string;
}

export interface Metafields {
  nodes: Metafield[];
}

export interface MetafieldDefinitionNode {
  id: string;
  name: string;
  namespace: string;
  key: string;
}

export interface MetafieldDefinitionsResponse {
  metafieldDefinitions: {
    nodes: MetafieldDefinitionNode[];
  };
}
