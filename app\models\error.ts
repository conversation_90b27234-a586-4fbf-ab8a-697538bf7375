import type { ErrorSource } from "./enums";
import { ErrorName, ErrorPlatform } from "./enums";

// Type alias for the context type
type ErrorContext =
    | ErrorSource.Customer
    | ErrorSource.Product
    | ErrorSource.Order
    | ErrorSource.User
    | ErrorSource.Testkit
    | ErrorSource.LabTest
    | ErrorSource.Result
    | ErrorSource.Metafield
    | ErrorSource.Metaobject
    | ErrorSource.MetafieldDefinition
    | ErrorSource.Webhook
    | ErrorSource.MetaobjectDefinition;

type Source = ErrorPlatform.Shopify | ErrorPlatform.Vital;

// Base Custom Error Class
class BaseCustomError extends Error {
    public context: ErrorContext | undefined;
    public source: Source | undefined;
    public originalError: any;

    constructor(
        message: string,
        context: ErrorContext,
        source: Source | undefined,
        originalError: any,
        errorName: string,
    ) {
        super(message);
        this.name = errorName;
        this.context = context;
        this.source = source;
        this.originalError = originalError;

        if (Error.captureStackTrace) {
            Error.captureStackTrace(this, this.constructor);
        }
    }
}

export class CustomError extends BaseCustomError {
    constructor(
        message: string,
        context: ErrorContext,
        source: Source,
        originalError: any,
    ) {
        const errorName = source === ErrorPlatform.Shopify
            ? ErrorName.ShopifyError
            : ErrorName.VitalError;

        super(message, context, source, originalError, errorName);
    }
}
