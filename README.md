# Styku Health Pass
The service for selling health passes or pay-per-scan to consumers

## Quick start
### Prerequisites

1. You must [download and install Node.js](https://nodejs.org/en/download/) if you don't already have it.
2. You must [create a Shopify partner account](https://partners.shopify.com/signup) if you don’t have one.
3. You must create a store for testing if you don't have one, either a [development store](https://help.shopify.com/en/partners/dashboard/development-stores#create-a-development-store) or a [Shopify Plus sandbox store](https://help.shopify.com/en/partners/dashboard/managing-stores/plus-sandbox-store).

### Setup

If you used the CLI to create the template, you can skip this section.

Using npm:

```shell
npm install
```
### App configuration
#### Update .env file:
 We need to add the environment variable to run the application
- `SHOPIFY_API_KEY`=Required (Get form the shopify App)
- `SHOPIFY_API_SECRET`=Required only on the production (Get form the shopify App)
- `SHOPIFY_APP_URL`=Required only on the production (Shopify app Url where Application is running)
- `SCOPES`=`read_customers,write_customers,read_metaobject_definitions,write_metaobject_definitions,read_metaobjects,write_metaobjects`
- `STYKU_BASE_URL`=`https://healthpassdev.calmpebble-397d4bfe.eastus.azurecontainerapps.io`
- `APPSTLE_BASE_URL`=`https://subscription-admin.appstle.com`
- `APPSTLE_API_KEY`=`<Appstle API key>`
- `DATABASE_URL`=`postgres://<user_name>:<password>@<host>:<port>/<database_name>?sslmode=require`
- `VITAL_API_KEY`=`<vital api key>`
- `VITAL_ENV`=`Sandbox or Production`
#### Note: 
To check what vital currently targeting:<br>
Check the value of VITAL_ENV in your environment variables. If it's not set, the code is targeting Production.
<hr>

#### Update Shopify.app.toml file:
Update the following in the Shopify.app.toml file:
- `client_id`: Shopify App client id
- `dev_store_url`: Shopify development store url
- `scopes`: Shopify app scopes

You can configure your apps locally with TOML files, then link them to apps in your Partner Dashboard and deploy your changes using Shopify CLI. You can also configure most of this configuration through the Partner Dashboard. The configuration in the Partner Dashboard always reflects the active version of your app. [Reffrence Link](https://shopify.dev/docs/apps/tools/cli/configuration)

### Local Development

Using npm:

```shell
npm run dev
```

Press P to open the URL to your app. Once you click install, you can start development.

Local development is powered by [the Shopify CLI](https://shopify.dev/docs/apps/tools/cli). It logs into your partners account, connects to an app, provides environment variables, updates remote config, creates a tunnel and provides commands to generate extensions.

## Running in Docker
To run the application in Docker:

```shell
docker-compose --env-file .env up 
```
## Deployment
### Application Storage
In this template we are using [Prisma](https://www.prisma.io/) to store session data, by default using  [Postgres](https://www.postgresql.org/docs) database.
The database is defined as a Prisma schema in `prisma/schema.prisma`.

### Build
Remix handles building the app for you, by running the command below with the package manager of your choice:

Using npm:
```shell
npm run build
```
## Hosting
When you're ready to set up your app in production, you can follow our deployment documentation to host your app on a cloud provider.<br>
[Deploy Node Remix App Container to Azure App Service](https://styku3d.atlassian.net/wiki/spaces/STYK/pages/**********/Deploy+Node+Remix+App+Container+to+Azure+App+Service)


## Resources

- [Remix Docs](https://remix.run/docs/en/v1)
- [Shopify App Remix](https://shopify.dev/docs/api/shopify-app-remix)
- [Introduction to Shopify apps](https://shopify.dev/docs/apps/getting-started)
- [App authentication](https://shopify.dev/docs/apps/auth)
- [Shopify CLI](https://shopify.dev/docs/apps/tools/cli)
- [App extensions](https://shopify.dev/docs/apps/app-extensions/list)
- [Shopify Functions](https://shopify.dev/docs/api/functions)
- [Getting started with internationalizing your app](https://shopify.dev/docs/apps/best-practices/internationalization/getting-started)
