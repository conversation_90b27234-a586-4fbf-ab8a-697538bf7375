export class graphQLUtils {
  /**
   * Constructs a GraphQL fragment for metafields based on namespace and key.
   * @param namespace - The namespace of the metafield.
   * @param key - The key of the metafield.
   * @returns A GraphQL fragment for querying metafields or an empty string.
   */
  static buildMetafieldFragment(namespace?: string, key?: string): string {
    if (namespace && key) {
      return `
        metafield(namespace: "${namespace}", key: "${key}") {
          key
          value
        }
      `;
    }
    return "";
  }

  /**
   * Creates a Shopify GraphQL ID in the global ID format.
   * 
   * @param ownerType - The type of Shopify resource (e.g., "Product", "Customer").
   * @param id - The unique identifier for the resource, which can be a number or string.
   * @returns A string representing the global ID in the format `gid://shopify/{ownerType}/{id}`.
   */
  static createGraphQLId(ownerType: string, id: number | string): string {
    return `gid://shopify/${ownerType}/${id}`;
  }
}
