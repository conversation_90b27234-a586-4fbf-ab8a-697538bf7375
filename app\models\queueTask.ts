import type { AdminApiContextWithoutRest } from "node_modules/@shopify/shopify-app-remix/dist/ts/server/clients";
import type { LineItem } from "./shopify/webhook/order";

export interface QueueTask {
    payload: LineItem[];
    admin: AdminApiContextWithoutRest;
    orderId: string;
    vendor: string;
    productType: string;
}

export interface QueueStats {
    attempts: number;
    elapsed: number;
}