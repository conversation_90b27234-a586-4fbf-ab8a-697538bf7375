import APIPayload from "~/models/apiPayload";
import type { LoginRequestPayload } from "~/models/styku/login";
import type { Password, Email, EmailCode, EmailPassword, ProfilePassword } from "~/models/styku/profile";
import type { ScanScheduleRequest } from "~/models/styku/scanSchedule";

export const API_METHODS = {
  POST: "POST",
  GET: "GET",
  PUT: "PUT",
};

export const HEADERS = {
  CONTENT_TYPE: "Content-Type",
  CONTENT_APPLICATION_JSON: "application/json",
  CONTENT_TEXT_PLAIN: "text/plain",
  AUTHORIZATION: "Authorization",
  X_STYKU_AUTHORIZATION: "X-Styku-Authorization",
  X_SHOPIFY_ACCESS_TOKEN: "X-Shopify-Access-Token",
};

const baseEndpoint = `${process.env.STYKU_BASE_URL}/api/v1`;
const appstleBaseEndpoint = `${process.env.APPSTLE_BASE_URL}/api/external/v2`;

export const BUSINESS_TYPE_ENDPOINT: string = `${baseEndpoint}/business-types`;
export const LOCATION_ENDPOINT: string = `${baseEndpoint}/locations`;
export const SCAN_SCHEDULE_ENDPOINT: string = `${LOCATION_ENDPOINT}/schedule-scan`;

export const API_PAYLOADS = {
  verifyProfile: new APIPayload<Email>(`${baseEndpoint}/account/email/verify-profile`, API_METHODS.POST),
  sendVerificationCode: new APIPayload<Email>(`${baseEndpoint}/account/email/send-verification-code`, API_METHODS.POST),
  verifyCode: new APIPayload<EmailCode>(`${baseEndpoint}/account/email/verify-code`, API_METHODS.POST),
  secureAccount: new APIPayload<ProfilePassword>(`${baseEndpoint}/account/secure`, API_METHODS.POST),
  login: new APIPayload<LoginRequestPayload>(`${baseEndpoint}/account/login`, API_METHODS.POST),
  resetPassword: new APIPayload<EmailPassword>(`${baseEndpoint}/account/reset-password`, API_METHODS.POST),
  signOut: new APIPayload<null>(`${baseEndpoint}/account/sign-out`, API_METHODS.POST),
  scheduleScan: new APIPayload<ScanScheduleRequest>(`${SCAN_SCHEDULE_ENDPOINT}`, API_METHODS.POST),
  getProfile: new APIPayload<null>(`${baseEndpoint}/profile`, API_METHODS.GET),
  changePassword: new APIPayload<Password>(`${baseEndpoint}/profile/change-password`, API_METHODS.PUT),
};

export const APPSTLE_API_PAYLOADS = {
  getCustomerSubscription: (customersId: string | null) => new APIPayload<null>(`${appstleBaseEndpoint}/subscription-customers-detail/valid/${customersId}?api_key=${process.env.APPSTLE_API_KEY}`, API_METHODS.GET)
}

function shopifyBaseEndpoint(storeUrl: string) {
  return `https://${storeUrl}/admin/api/2024-07/graphql.json`;
}

export const SHOPIFY_GRAPHQL_API_PAYLOADS = {
  updateMetaobject: (storeUrl: string) => new APIPayload<null>(shopifyBaseEndpoint(storeUrl), API_METHODS.POST)
}
