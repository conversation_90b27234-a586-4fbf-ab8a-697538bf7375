import type { AdminApiContextWithoutRest } from "node_modules/@shopify/shopify-app-remix/dist/ts/server/clients";
import { CustomError } from "~/models/error";
import { ERROR_MESSAGES } from "~/utils/stringConstants";
import type { Customer } from "~/models/shopify/customer";
import { ErrorSource, ErrorPlatform } from "~/models/enums";
import { METAFIELD } from "~/utils/shopifyConstants";

/**
 * Get Shopify customer with the given metafields
 * @param admin Authenticated admin
 * @param customerId Shopify customer id
 * @param metafieldKeys Metafield keys
 * @returns Shopify customer
 */
export async function getCustomerWithMetafields(
  admin: AdminApiContextWithoutRest,
  customerId: string,
): Promise<Customer | undefined> {
  try {
    const responseAwaiter = await admin.graphql(
      `#graphql
      query customer($id: ID!) {
        customer(id: $id) {
          id
          email
          vitalUserIdMetafield: metafield (namespace: "${METAFIELD.NAMESPACE.VITAL}", key: "${METAFIELD.KEY.USER_ID}") {
      			key,
      			value
    			}
          totalPPSMetafield: metafield (namespace: "${METAFIELD.NAMESPACE.STYKU}", key: "${METAFIELD.KEY.AVAILABLE_SCAN_BUNDLES}") {
      			key,
      			value
    			}
          stykuProfileIdMetafield: metafield (namespace: "${METAFIELD.NAMESPACE.STYKU}", key: "${METAFIELD.KEY.PROFILE_ID}") {
      			key,
      			value
    			}
        }
      }`,
      {
        variables: {
          id: customerId,
        },
      },
    );

    const response = await responseAwaiter.json();

    return response.data?.customer;
  } catch (error) {
    throw new CustomError(
      `${ERROR_MESSAGES.SHOPIFY.GET_CUSTOMER}: "${customerId}"`,
      ErrorSource.Customer,
      ErrorPlatform.Shopify,
      error,
    );
  }
}
