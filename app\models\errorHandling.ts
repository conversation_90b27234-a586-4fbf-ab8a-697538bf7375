import type { ShopifyMetaobjectFieldInput, ShopifyMetaobjectUpsertInput } from "./shopify/metaobject";

export interface ErrorHandlePayload {
  error?: any,
  context?: any,
  isCritical: boolean;
  createdAt: Date,
  updatedAt: Date,
}

export interface ErrorHandlingParams {
  ownerType: string;
  ownerId: number;
  namespace: string;
  key: string;
  type: string;
}

export class ErrorLogMetaobject {
  fields: ShopifyMetaobjectFieldInput[];

  constructor(fields: ShopifyMetaobjectFieldInput[]) {
    this.fields = fields;
  }

  // Static method to create an instance from an order object
  static create(payload: ErrorHandlePayload): ShopifyMetaobjectUpsertInput {
    const fields: ShopifyMetaobjectFieldInput[] = [
      {
        key: "context",
        value: payload.context,
      },
      {
        key: "error",
        value: JSON.stringify(payload.error)
      },
      {
        key: "is_critical",
        value: payload.isCritical.toString()
      },
      {
        key: "created_at",
        value: new Date(payload.createdAt).toISOString()
      },
      {
        key: "updated_at",
        value: new Date(payload.updatedAt).toISOString()
      },
    ];
    return new ErrorLogMetaobject(fields);
  }
};
