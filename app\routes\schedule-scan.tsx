import { json, type LoaderFunctionArgs } from "@remix-run/node";
import scheduleScan from "~/api/styku/scanSchedule";
import {
  checkAndCreateMetaobjectDefinition,
  upsertMetaobject,
} from "~/api/shopify/graphQL/metaObject";
import type APIResponse from "~/models/apiResponse";
import type {
  ScanScheduleRequest,
  ScanScheduleResponse,
} from "~/models/styku/scanSchedule";
import ScanSchedule from "~/models/styku/scanSchedule";
import { authenticate } from "~/shopify.server";
import { SCHEDULED_SCAN_META_DEFINITION } from "~/utils/shopifyConstants";

export async function action({ request }: LoaderFunctionArgs) {
  try {
    const { admin } = await authenticate.public.appProxy(request);

    const scanSchedulePayload: ScanScheduleRequest = await request.json();

    // Schedule a scan through Styku service
    const scanScheduleResponse: APIResponse<ScanScheduleResponse> =
      await scheduleScan(scanSchedulePayload);

    if (scanScheduleResponse.status === 200 && admin) {
      // Check for the scheduled scan definition in Shopify and create if it is not present
      await checkAndCreateMetaobjectDefinition(
        admin,
        SCHEDULED_SCAN_META_DEFINITION,
      );

      // Push scan schedule data to Shopify
      await upsertMetaobject(
        admin,
        SCHEDULED_SCAN_META_DEFINITION.type,
        scanScheduleResponse.data!.id.toString(),
        ScanSchedule.toShopifyMetaobject(scanScheduleResponse.data!),
      );
    }
    return json(scanScheduleResponse);
  } catch (error) {
    console.error(error);
    throw error;
  }
}
