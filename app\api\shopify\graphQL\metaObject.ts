import type { AdminApiContextWithoutRest } from "node_modules/@shopify/shopify-app-remix/dist/ts/server/clients";
import type { ShopifyMetaobjectDefinition, ShopifyMetaobjectUpsertInput } from "~/models/shopify/metaobject";
import type { FreeProductMetaobject } from "~/models/shopify/productResponse";
import { ErrorPlatform, ErrorSource } from "~/models/enums";
import { ERROR_MESSAGES } from "~/utils/stringConstants";
import { CustomError } from "~/models/error";

// Check for the existing metaobject definition and create a new definition if it is not exists
export async function checkAndCreateMetaobjectDefinition(
  admin: AdminApiContextWithoutRest,
  definition: ShopifyMetaobjectDefinition
): Promise<string | null> {
  // Check for the definition in Shopify
  let definitionId = await checkMetaobjectDefinitionByType(admin, definition.type);

  if (!definitionId) {
    // If the definition is not present then it will create a new one
    definitionId = await createMetaobjectDefinition(admin, definition);
    console.log(`Created a ${definition.type} definition with id ${definitionId}`);
  } else {
    console.warn(`The ${definition.type} definition is already exists with id ${definitionId}`);
  }

  return definitionId;
}

// Check if a metaobject definition exists for the specified type
export async function checkMetaobjectDefinitionByType(
  admin: AdminApiContextWithoutRest,
  definitionType: string
): Promise<string | null> {
  const responseAwaiter = await admin.graphql(
    `#graphql
    query metaobjectDefinitionByType($typeName: String!) {
        metaobjectDefinitionByType(type: $typeName) {
            id
        }
    }`,
    {
      variables: {
        typeName: definitionType,
      },
    }
  );

  const response = await responseAwaiter.json();

  return response.data?.metaobjectDefinitionByType?.id;
}

// Create a new metaobject definition
export async function createMetaobjectDefinition(
  admin: AdminApiContextWithoutRest,
  definition: ShopifyMetaobjectDefinition
): Promise<string | null> {
  try {
    const responseAwaiter = await admin.graphql(
      `#graphql
      mutation metaobjectDefinitionCreate($definition: MetaobjectDefinitionCreateInput!) {
          metaobjectDefinitionCreate(definition: $definition) {
              metaobjectDefinition {
                  id
              }
              userErrors {
                  field
                  message
                  code
              }
          }
      }`,
      {
        variables: {
          definition: {
            access: {
              admin: "PUBLIC_READ_WRITE",
              storefront: "PUBLIC_READ",
            },
            name: definition.title,
            type: definition.type,
            fieldDefinitions: definition.fieldDefinitions,
          },
        },
      }
    );

    const response = await responseAwaiter.json();

    const result = response.data?.metaobjectDefinitionCreate;

    if (result?.userErrors?.length > 0) {
      throw new CustomError(
        `${ERROR_MESSAGES.SHOPIFY.CREATE_METAOBJECT_DEFINITION} definitionType: "${definition.type}" definition title: ${definition.title}.`,
        ErrorSource.MetaobjectDefinition,
        ErrorPlatform.Shopify,
        result?.userErrors
      );
    }

    return result?.metaobjectDefinition?.id;
  } catch (error) {
    if (error instanceof CustomError) {
      throw error;
    }
    throw new CustomError(
      `${ERROR_MESSAGES.FAILURE} definitionType: "${definition.type}" definition title: ${definition.title}.`,
      ErrorSource.MetaobjectDefinition,
      ErrorPlatform.Shopify,
      error
    );
  }
}

// Create or update a metaobject with the provided data
export async function upsertMetaobject(
  admin: AdminApiContextWithoutRest,
  definitionType: string,
  handle: string,
  metaobjectData: ShopifyMetaobjectUpsertInput,
) {
  try {
    const responseAwaiter = await admin.graphql(
      `#graphql
    mutation metaobjectUpsert($handle: MetaobjectHandleInput!, $metaobject: MetaobjectUpsertInput!) {
        metaobjectUpsert(handle: $handle, metaobject: $metaobject) {
            metaobject {
                id
            }
            userErrors {
                field
                message
            }
        }
    }`,
      {
        variables: {
          handle: {
            handle: handle,
            type: definitionType,
          },
          metaobject: metaobjectData,
        },
      }
    );

    const response = await responseAwaiter.json();

    const result = response.data?.metaobjectUpsert;

    if (result?.userErrors?.length > 0) {
      throw new CustomError(
        `${ERROR_MESSAGES.SHOPIFY.UPSERT_METAOBJECT} definitionType: "${definitionType}" handle: "${handle}".`,
        ErrorSource.Metaobject,
        ErrorPlatform.Shopify,
        result?.userErrors,
      );
    }

    return result?.metaobject?.id;
  } catch (error) {
    if (error instanceof CustomError) {
      throw error;
    }
    throw new CustomError(
      `${ERROR_MESSAGES.FAILURE} definitionType: "${definitionType}" handle: "${handle}".`,
      ErrorSource.Metaobject,
      ErrorPlatform.Shopify,
      error,
    );
  }
}

/**
 * Creates a metaobject in Shopify using the GraphQL API.
 * 
 * @param {AdminApiContextWithoutRest} admin - The admin API context for GraphQL operations.
 * @param {string} definitionType - The type of the metaobject
 * @param {ShopifyMetaobjectUpsertInput} metaobjectData - The data for the metaobject to be created.
 */
export async function createMetaobject(
  admin: AdminApiContextWithoutRest,
  definitionType: string,
  metaobjectData: ShopifyMetaobjectUpsertInput,
): Promise<string | undefined> {
  try {
    const responseAwaiter = await admin.graphql(
      `#graphql
      mutation CreateMetaobject($metaobject: MetaobjectCreateInput!) {
        metaobjectCreate( metaobject: $metaobject) {
          metaobject {
            id
          }
          userErrors {
            field
            message
          }
        }
      }`,
      {
        variables: {
          metaobject: {
            type: definitionType,
            ...metaobjectData,
          },
        },
      }
    );

    const response = await responseAwaiter.json();

    const result = response.data?.metaobjectCreate;

    if (result?.userErrors?.length > 0) {
      throw new CustomError(
        `${ERROR_MESSAGES.SHOPIFY.CREATE_METAOBJECT} definitionType: "${definitionType}".`,
        ErrorSource.Metaobject,
        ErrorPlatform.Shopify,
        result?.userErrors,
      );
    }

    return result?.metaobject?.id;
  } catch (error) {
    if (error instanceof CustomError) {
      throw error;
    }
    throw new CustomError(
      `${ERROR_MESSAGES.SHOPIFY.CREATE_METAOBJECT} definitionType: "${definitionType}".`,
      ErrorSource.Metaobject,
      ErrorPlatform.Shopify,
      error,
    );
  }
}

/**
 * Get a metaobject in Shopify using the GraphQL API.
 *
 * @param {AdminApiContext<ShopifyRestResources>} admin - The admin API context for GraphQL operations.
 * @param {string} metaobjectId - The ID of the metaobject to fetch.
 * @returns {Promise<FreeProductMetaobject | undefined>} - The ID of the metaobject or undefined if not found.
 */
export async function getMetaobjectById(
  admin: AdminApiContextWithoutRest,
  metaobjectId: string,
): Promise<FreeProductMetaobject | undefined> {
  try {
    const responseAwaiter = await admin.graphql(
      `#graphql
      query($id: ID!) {
        metaobject(id: $id) {
          id
          displayName
          handle
          productId: field(key: "product") {value}
          productTitle: field(key: "title") { value }
          quantity: field(key: "number_of_quantity") { value }
          vendor: field(key: "vendor") { value }
        }
      }`,
      {
        variables: {
          id: metaobjectId,
        },
      },
    );
    const response = await responseAwaiter.json();
    return response.data?.metaobject;
  } catch (error) {
    throw new CustomError(
      `${ERROR_MESSAGES.SHOPIFY.FETCHED_METAOBJECT} ID: "${metaobjectId}".`,
      ErrorSource.Metaobject,
      ErrorPlatform.Shopify,
      error,
    );
  }
}