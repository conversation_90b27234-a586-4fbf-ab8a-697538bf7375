{"name": "health-pass-app", "private": true, "scripts": {"build": "vite build && vite build --ssr", "dev": "shopify app dev", "config:link": "shopify app config link", "generate": "shopify app generate", "deploy": "shopify app deploy", "config:use": "shopify app config use", "env": "shopify app env", "start": "remix-serve ./build/server/index.js", "docker-start": "npm run setup && npm run start", "setup": "prisma generate && prisma migrate deploy", "lint": "eslint --cache --cache-location ./node_modules/.cache/eslint .", "shopify": "shopify", "prisma": "prisma", "graphql-codegen": "graphql-codegen", "vite": "vite"}, "type": "module", "engines": {"node": "^18.20 || ^20.10 || >=21.0.0"}, "dependencies": {"@prisma/adapter-pg": "^6.5.0", "@prisma/client": "^6.2.1", "@remix-run/dev": "^2.16.1", "@remix-run/fs-routes": "^2.16.1", "@remix-run/node": "^2.16.1", "@remix-run/react": "^2.16.1", "@remix-run/serve": "^2.16.1", "@shopify/app-bridge-react": "^4.1.2", "@shopify/cli": "^3.63.1", "@shopify/polaris": "^12.0.0", "@shopify/shopify-app-remix": "^3.7.0", "@shopify/shopify-app-session-storage-prisma": "^6.0.0", "@tryvital/vital-node": "^3.1.53", "better-queue": "^3.8.12", "isbot": "^5.1.0", "pg": "^8.14.1", "prisma": "^6.2.1", "react": "^18.2.0", "react-dom": "^18.2.0", "vite-tsconfig-paths": "^5.0.1"}, "devDependencies": {"@remix-run/eslint-config": "^2.7.1", "@shopify/api-codegen-preset": "^0.0.7", "@types/better-queue": "^3.8.6", "@types/eslint": "^8.40.0", "@types/node": "^20.6.3", "@types/pg": "^8.11.6", "@types/react": "^18.2.31", "@types/react-dom": "^18.2.14", "eslint": "^8.42.0", "eslint-config-prettier": "^9.1.0", "prettier": "^3.2.4", "typescript": "^5.2.2", "vite": "^6.2.2"}, "workspaces": {"packages": ["extensions/*"]}, "trustedDependencies": ["@shopify/plugin-cloudflare"], "author": "<PERSON><PERSON><PERSON>"}