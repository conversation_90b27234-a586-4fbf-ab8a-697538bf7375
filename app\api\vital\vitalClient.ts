import { VitalClient, VitalEnvironment } from "@tryvital/vital-node";

class VitalClientService {
    private static instance: VitalClient | null = null;

    private constructor() { }

    public static async getInstance(): Promise<VitalClient> {
        if (this.instance === null) {
            const env = process.env.VITAL_ENV as keyof typeof VitalEnvironment;
            const environment = env && VitalEnvironment[env] ? VitalEnvironment[env] : VitalEnvironment.Production;

            this.instance = new VitalClient({
                apiKey: process.env.VITAL_API_KEY || '',
                environment: environment,
            });
        }

        return this.instance;
    }
}

export default VitalClientService;
