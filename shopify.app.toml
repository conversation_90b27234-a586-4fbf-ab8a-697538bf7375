# Learn more about configuring your app at https://shopify.dev/docs/apps/tools/cli/configuration

client_id = "b8733809c24cbb6bfdbb03fcb92bf42a"
application_url = "https://valves-calculations-juvenile-musician.trycloudflare.com"
embedded = true
name = "health-pass-dev"
handle = "health-pass-dev"

[build]
automatically_update_urls_on_dev = true
dev_store_url = "health-pass-dev.myshopify.com"
include_config_on_deploy = true

[webhooks]


  [[webhooks.subscriptions]]
  topics = [ "app/uninstalled" ]
  uri = "/webhooks/app/uninstalled"

  [[webhooks.subscriptions]]
  topics = [ "app/scopes_update" ]
  uri = "/webhooks/app/scopes-update"

  [[webhooks.subscriptions]]
  topics = [ "orders/create" ]
  uri = "/webhooks/app/orders-create"

[access_scopes]
# Learn more at https://shopify.dev/docs/apps/tools/cli/configuration#access_scopes
scopes = "read_customers,read_metaobject_definitions,read_metaobjects,read_orders,write_assigned_fulfillment_orders,write_customers,write_merchant_managed_fulfillment_orders,write_metaobject_definitions,write_metaobjects,write_orders,write_products,unauthenticated_read_customers,unauthenticated_write_customers,unauthenticated_read_customer_tags"

[auth]
redirect_urls = [
  "https://valves-calculations-juvenile-musician.trycloudflare.com/auth/callback",
  "https://valves-calculations-juvenile-musician.trycloudflare.com/auth/shopify/callback",
  "https://valves-calculations-juvenile-musician.trycloudflare.com/api/auth/callback"
]

[app_proxy]
url = "https://valves-calculations-juvenile-musician.trycloudflare.com"
subpath = "styku"
prefix = "apps"

[pos]
embedded = false
