import { createRefund, getOrderDetails } from "~/api/shopify/graphQL/order";
import { graphQLUtils } from "~/api/shopify/graphQL/utility";
import type { RefundInput } from "~/models/shopify/order";
import type { OrderResponse } from "~/models/shopify/webhook/order";
import {
    REFUND,
    OWNER_TYPE,
    PRODUCT_VENDOR,
    STATUS,
} from "~/utils/shopifyConstants";

/**
 * Processes a refund for health products in an order.
 *
 * @param admin - The admin context for API calls.
 * @param orderId - The numeric ID of the order to refund.
 * @param note - Optional note to include in the refund.
 * @returns The refund response from Shopify.
 */
export async function processRefund(
    admin: any,
    payload: OrderResponse
) {
    const graphqlOrderId = graphQLUtils.createGraphQLId(OWNER_TYPE.ORDER, payload.id);

    // get the order details
    const orderDetails = await getOrderDetails(admin, graphqlOrderId);

    // Filter health products
    const healthProducts = payload.line_items.filter((item) => item.vendor === PRODUCT_VENDOR.VITAL);

    if (healthProducts) {
        const parentTransaction = orderDetails.transactions.find(
            (transaction) => transaction.status === STATUS.SUCCESS,
        );

        if (!parentTransaction) {
            throw new Error("No successful transaction found for refund.");
        }

        const refundLineItems = healthProducts.map((item) => ({
            lineItemId: graphQLUtils.createGraphQLId(OWNER_TYPE.LINEITEM, item.id),
            quantity: item.quantity,
        }));

        const totalRefundAmount = healthProducts.reduce(
            (total, item) =>
                total + parseFloat(item.price) * item.quantity,
            0,
        );

        // Create RefundInput payload
        const input: RefundInput = {
            orderId: graphqlOrderId,
            note: REFUND.staffNote,
            refundLineItems,
            notify: true,
            currency: payload.currency,
            transactions: [
                {
                    orderId: orderDetails.id,
                    gateway: parentTransaction.gateway,
                    kind: REFUND.kind.refund,
                    amount: totalRefundAmount.toFixed(2),
                    parentId: parentTransaction.id,
                },
            ],
        };

        const refundResponse = await createRefund(admin, input);
        return refundResponse;
    }
}
