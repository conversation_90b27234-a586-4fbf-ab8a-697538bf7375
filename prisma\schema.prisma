// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider        = "prisma-client-js"
  previewFeatures = ["driverAdapters"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Session {
  id            String    @id
  shop          String
  state         String
  isOnline      Boolean   @default(false)
  scope         String?
  expires       DateTime? @db.Timestamp(6)
  accessToken   String
  userId        BigInt?
  accountOwner  <PERSON>olean   @default(false)
  collaborator  <PERSON><PERSON><PERSON>?  @default(false)
  email         String?
  emailVerified Boolean?  @default(false)
  firstName     String?
  lastName      String?
  locale        String?

  @@map("sessions")
}

model OrdersWebhookTask {
  id             BigInt    @id @default(autoincrement())
  webhook_id     String
  topic          String
  payload        Json
  priority       Int       @default(0)
  status         String    @default("pending")
  last_error     String?
  created_at     DateTime  @default(now())
  updated_at     DateTime
  lineItems      OrderLineItem[]

  @@index([topic])
  @@map("orders_webhook_tasks")
}

model OrderProcessingError {
  id             BigInt   @id @default(autoincrement())
  orderId        String
  productId      String
  productTitle   String
  quantity       Int
  failedQuantity Int
  errorMessage   String
  createdAt      DateTime @default(now())

  @@map("order_processing_errors")
}

model OrderLineItem {
  id               BigInt           @id @default(autoincrement())
  orderId          BigInt
  order            OrdersWebhookTask @relation(fields: [orderId], references: [id])
  lineItemId       String           // Shopify line item ID
  productId        String           // Shopify product ID
  productTitle     String
  vendor           String
  productType      String
  quantity         Int
  processedCount   Int              @default(0)
  failedCount      Int              @default(0)
  status           String           @default("pending") // pending, processing, completed, failed
  lastError        String?
  attempts         Int              @default(0)
  maxAttempts      Int              @default(3)
  retryAfter       DateTime?
  createdAt        DateTime         @default(now())
  updatedAt        DateTime         @updatedAt

  @@unique([orderId, lineItemId])
  @@index([orderId])
  @@index([status])
  @@index([lineItemId])
  @@map("order_line_items")
}
