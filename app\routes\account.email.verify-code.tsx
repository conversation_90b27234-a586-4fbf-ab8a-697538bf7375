import { json, type ActionFunctionArgs } from "@remix-run/node";
import { verifyCode } from "~/api/styku/account";
import type APIResponse from "~/models/apiResponse";
import type { Profile } from "~/models/styku/profile";
import { authenticate } from "~/shopify.server";

/**
 * Verify an email verification code
 * Returns:
 * 1. 200 - On verify. Return profile data if exists. Return null if not exists
 * 2. 601 - Invalid email address
 * 3. 604 - Invalid code
 * 4. 500 - Other errors
 */
export async function action({ request }: ActionFunctionArgs) {
  await authenticate.public.appProxy(request);

  const { email, code } = await request.json();
  const response: APIResponse<Profile> = await verifyCode(email, code);
  return json(response);
}
