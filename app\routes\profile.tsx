import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { getProfile } from "~/api/styku/profile";
import {
  findShopifyCustomerById,
  updateShopifyCustomerDetailsAsync,
} from "~/api/shopify/rest/customer";
import type APIResponse from "~/models/apiResponse";
import type { Profile } from "~/models/styku/profile";
import { authenticate } from "~/shopify.server";
import { HEADERS } from "~/utils/apiConstants";
import { SHOPIFY_QUERY_PARAMS_KEY } from "~/utils/shopifyConstants";

/**
 * Get request for the /profile
 * Returns:
 * 1. 200 - On success
 * 2. 602 - Profile not found
 * 8. 500 - Other errors
 */
export async function loader({ request }: LoaderFunctionArgs) {
  const { admin, session } = await authenticate.public.appProxy(request);
  const { searchParams } = new URL(request.url);

  const loggedInCustomerId: string | null = searchParams.get(
    SHOPIFY_QUERY_PARAMS_KEY.loggedInCustomerId,
  );

  const authToken: string | null = request.headers.get(
    HEADERS.X_STYKU_AUTHORIZATION,
  );
  const response: APIResponse<Profile> = await getProfile(authToken);

  if (response.status === 200 && admin) {
    const { firstName, lastName } = response.data ?? {};

    const shopifyCustomer = await findShopifyCustomerById(
      admin,
      session,
      loggedInCustomerId,
    );

    if (shopifyCustomer) {
      if (
        firstName != shopifyCustomer.first_name ||
        lastName != shopifyCustomer.last_name
      ) {
        await updateShopifyCustomerDetailsAsync(
          admin,
          session,
          shopifyCustomer.id,
          { firstName, lastName },
        );
      }
    } else {
      console.log("No Shopify customer found.");
    }
  }
  return json(response);
}
