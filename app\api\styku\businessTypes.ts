import type BusinessType from "~/models/styku/businessType";
import { BUSINESS_TYPE_ENDPOINT } from "~/utils/apiConstants";

export async function getBusinessTypes(): Promise<BusinessType[]> {
  try {
    const response = await fetch(BUSINESS_TYPE_ENDPOINT);

    if (!response.ok) {
      throw new Error(`Failed to fetch business types from the server. Error code ${response.status}`);
    }

    const businessTypes: BusinessType[] = await response.json();
    return businessTypes;
  } catch (error) {
    console.error(`Error while fetching business types from the server. ${error}`);
    throw error;
  }
}
