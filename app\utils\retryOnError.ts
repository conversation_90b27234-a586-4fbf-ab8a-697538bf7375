/**
 * Executes a function with retry logic for specified transient errors.
 * Retries on defined status codes and invokes a failure handler after max retries.
 *
 * @param func - The asynchronous function to execute.
 * @param retryableStatusCodes - Status codes that trigger a retry.
 * @param options - Retry configuration (retries, delay).
 * @returns - The result of the successful function execution.
 * @throws Error - If non-retryable or unexpected failure occurs.
 */
export async function retryOnError<T>(
    func: () => Promise<T>,
    retryableStatusCodes: number[],
    options: { retries: number; delay: number }
) {
    const { retries, delay } = options;
    let attempt = 0;

    while (attempt < retries) {
        try {
            // Return result on success
            return await func();
        } catch (error: any) {
            if (!retryableStatusCodes.includes(error?.statusCode)) throw error

            attempt++;
            if (attempt < retries) {
                console.warn(`Retry attempt ${attempt} after ${delay}ms`);

                await new Promise((resolve) => setTimeout(resolve, delay));
            } else throw error
        }
    }
    throw new Error("Retry logic failed unexpectedly.");
}
