import type { ClientFacingLabTest } from "@tryvital/vital-node/api";

export interface Product {
    id: string;
    title: string;
    handle: string;
    vendor: string;
    productType: string;
    metafield: MetafieldNode;
    labTestIdMetafield: MetafieldNode;
    assignedFreeProductsMetafiled: MetafieldNode;
    variants: VariantConnection;
}

export interface FreeProductMetaobject {
    id: string,
    displayName: string,
    handle: string,
    productId: { value: string },
    productTitle: { value: string },
    quantity: { value: number }
    vendor: { value: string }
}

export interface PageInfo {
    hasNextPage: boolean;
    endCursor: string | null;
}

export interface ProductsResponse {
    edges: { node: Product }[];
    pageInfo: PageInfo;
}

export interface MetafieldNode {
    key: string;
    value: string;
}

export interface SyncProductsResponse {
    existingProducts: ProductsResponse;
    newProducts: ClientFacingLabTest[];
}

export interface ProductVariant {
    id: string;
    price: string;
    createdAt: string;
}

export interface VariantEdge {
    node: ProductVariant;
}

export interface VariantConnection {
    edges: VariantEdge[];
}

export interface CreatedProduct {
    id: string;
    title: string;
    handle: string;
    status: string;
    variants: VariantConnection;
}

export interface CreateProductResponse {
    product: CreatedProduct;
}
