import type APIPayload from "~/models/apiPayload";
import type APIResponse from "~/models/apiResponse";
import { APPSTLE_API_PAYLOADS } from "~/utils/apiConstants";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "~/utils/stringConstants";
import { makeRequest } from "../fetch";
import type { SubscriptionDetails } from "~/models/appstle/subscription";

/**
 * Get Subscription details
 * @param customersId Customer Id
 * @returns Subscription details
 */
export async function getCustomerSubscriptions(customersId: string | null): Promise<APIResponse<SubscriptionDetails[]>> {
  const apiPayload: APIPayload<null> = APPSTLE_API_PAYLOADS.getCustomerSubscription(customersId);
  apiPayload.successMessage = SUCCESS_MESSAGES.APPSTLE.GET_SUBSCRIPTIONS_SUCCESS;
  apiPayload.failureMessage = ERROR_MESSAGES.APPSTLE.GET_SUBSCRIPTIONS;

  return await makeRequest<null, SubscriptionDetails[]>(apiPayload);
}
