import type internal from "stream";
import VitalClientService from "./vitalClient";
import { VitalError } from "@tryvital/vital-node";
import { ERROR_MESSAGES } from "~/utils/stringConstants";
import { CustomError } from "~/models/error";
import { ErrorSource, ErrorPlatform } from "~/models/enums";

export async function getResultPdf(orderId: string): Promise<internal.Readable | undefined> {
    try {
        const client = await VitalClientService.getInstance();

        return await client.labTests.getResultPdf(orderId);
    } catch (error) {
        if (error instanceof VitalError) {
            throw new CustomError(
                `${ERROR_MESSAGES.VITAL.GET_RESULT}: "${orderId}"`,
                ErrorSource.Result,
                ErrorPlatform.Vital,
                error,
            );
        } else {
            throw new CustomError(
                `${ERROR_MESSAGES.FAILURE}: "${orderId}"`,
                ErrorSource.Result,
                ErrorPlatform.Vital,
                error,
            );
        }
    }
}
