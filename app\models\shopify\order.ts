export interface OrderDetailsResponse {
  id: string;
  lineItems: {
    nodes: LineItem[];
  };
  transactions: Transaction[];
  fulfillmentOrders: {
    nodes: FulfillmentOrder[];
  };
}

export interface LineItem {
  id: string;
  title: string;
  name: string;
  quantity: number;
  originalUnitPriceSet: {
    presentmentMoney: {
      amount: string;
      currencyCode: string;
    };
  };
  product: {
    productType: string;
    vendor: string;
  };
}

export interface Transaction {
  status: string;
  gateway: string;
  paymentId: string;
  id: string;
}

export interface FulfillmentOrder {
  id: string;
  orderId: string;
  orderName: string;
  status: string;
  lineItems: {
    nodes: FulfillmentLineItem[];
  };
}

export interface FulfillmentLineItem {
  id: string;
  productTitle: string;
  totalQuantity: number;
  vendor: string;
}

export interface RefundInput {
  orderId: string;
  note: string;
  refundLineItems: {
    lineItemId: string;
    quantity: number;
  }[];
  notify: boolean;
  currency: string;
  transactions: {
    orderId: string;
    gateway: string;
    kind: string;
    amount: string;
    parentId: string;
  }[];
}

export interface RefundCreateResponse {
  refund: {
    id: string;
    note: string;
    totalRefundedSet: {
      presentmentMoney: {
        amount: string;
        currencyCode: string
      };
    };
  };
}
