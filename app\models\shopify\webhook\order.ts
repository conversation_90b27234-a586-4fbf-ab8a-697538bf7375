import type { Customer } from "./customer";

export interface OrderResponse {
  id: number;
  name: string;
  currency: string;
  financial_status: string;
  fulfillment_status: string;
  confirmation_number: string;
  confirmed: boolean;
  created_at: string;
  line_items: LineItem[];
  customer: Customer;
  shipping_address: ShippingAddress;
  billing_address: BillingAddress;
}

export interface LineItem {
  id: number;
  title: string;
  price: string;
  product_id: number;
  quantity: number;
  vendor: string;
  variant_id: number;
  variant_title: string;
  product_type: string; 
}

export interface BillingAddress {
  name: string;
  first_name: string;
  last_name: string;
  address1: string;
  address2?: string | null;
  company?: string | null;
  city: string;
  province: string;
  province_code: string;
  zip: string;
  country: string;
  country_code: string;
  phone: string;
}

export interface ShippingAddress {
  name: string;
  address1: string;
  address2?: string;
  city: string;
  province: string;
  province_code: string;
  zip: string;
  country: string;
  country_code: string;
  phone: string;
}
