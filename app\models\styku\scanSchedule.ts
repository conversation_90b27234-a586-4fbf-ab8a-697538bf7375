import type { ShopifyMetaobjectUpsertInput } from "../shopify/metaobject";

export interface ScanScheduleRequest {
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber: string;
  userId: number;
}

export interface ScanScheduleResponse extends ScanScheduleRequest {
  id: number;
}

export default class ScanSchedule {
  id: number = 0;
  firstName: string = "";
  lastName: string = "";
  email: string = "";
  phoneNumber: string = "";
  userId: number = 0;

  static getData(data: ScanSchedule) {
    return {
      firstName: data.firstName,
      lastName: data.lastName,
      email: data.email,
      phoneNumber: data.phoneNumber,
      userId: data.userId,
    }
  }

  static getInstance(formData: FormData): ScanSchedule {
    const instance: ScanSchedule = new ScanSchedule();
    instance.firstName = formData.get("first_name")?.toString() ?? "";
    instance.lastName = formData.get("last_name")?.toString() ?? "";
    instance.email = formData.get("email")?.toString() ?? "";
    instance.phoneNumber = formData.get("phone_number")?.toString() ?? "";
    instance.userId = Number.parseInt(formData.get("user_id")?.toString() ?? "0");

    return instance;
  }

  validateData(): boolean {
    this.firstName = this.firstName.trim();
    this.lastName = this.lastName.trim();
    this.email = this.email.trim();
    this.phoneNumber = this.phoneNumber.trim();

    return !!(this.firstName && this.lastName && this.email && this.phoneNumber && this.userId > 0);
  }

  static toShopifyMetaobject(obj: ScanScheduleResponse): ShopifyMetaobjectUpsertInput {
    return {
      fields: [
        {
          key: "first_name",
          value: obj.firstName,
        },
        {
          key: "last_name",
          value: obj.lastName,
        },
        {
          key: "email",
          value: obj.email,
        },
        {
          key: "phone_number",
          value: obj.phoneNumber,
        },
        {
          key: "user_id",
          value: obj.userId.toString(),
        },
      ],
    };
  }
}
