export const EMPTY_STATE_STRING = {
  SYNC_PRODUCT: {
    heading: "No products available",
    message: "It looks like there are no products available from Vital.",
    buttonText: "Sync Vital products into Shopify"
  }
}

export const ERROR_MESSAGES = {
  FAILURE: "Failure",
  WEBHOOK_PROCESSING: "Error while processing webhook data",
  SHOPIFY: {
    GET_PRODUCT: "Error while getting product",
    GET_CUSTOMER: "Error while getting customer",
    CREATE_METAOBJECT: "Error while creating a metaobject",
    FETCHED_METAOBJECT: "Error while feathing a metaobject",
    UPSERT_METAOBJECT: "Error while Upsert a metaobject",
    CREATE_METAFIELD_DEFINITION: "Failed to create metafield definition",
    GET_METAFIELD: "Error while getting the meta field",
    UPDATE_METAFIELD: "Error while updating the meta field",
    CREATE_METAOBJECT_DEFINITION: "Error while creating a metaobject definition",
    SHOP_NOT_DEFINED: "Shop is not defined in the headers",
    SESSION_NOT_FOUND: "Session not found for shop",
    GET_ORDER: "Error while fetcing order",
    CREATE_REFUND: "Error while creating refund",
  },
  STYKU: {
    VERIFY_CODE: "Error while verifying a code",
    VERIFY_PROFILE: "Error while verifying the profile",
    SEND_VERIFICATION_CODE: "Error while sending a verification code",
    SECURE_ACCOUNT: "Error while securing the profile",
    LOGIN: "Error while logged in the profile",
    RESET_PASSWORD: "Error while reset the password",
    CHANGE_PASSWORD: "Error while changing the password",
    SIGN_OUT: "Error while signing out",
    GET_PROFILE: "Error while getting the profile details",
  },
  VITAL: {
    GET_USER: "Error while finding a user in vital with client ID",
    CREATE_USER: "Error while creating a user in vital with client ID",
    CREATE_ORDER: "Error while creating a registrable test kit order in Vital for user ID",
    GET_LAB_TESTS: 'Error while fetching latests',
    GET_RESULT: "Error while finding a test result in vital with order ID"

  },
  APPSTLE: {
    GET_SUBSCRIPTIONS: "Error while getting the subscription details",
  }
};

export const SUCCESS_MESSAGES = {
  SUCCESS: "Success",
  WEBHOOK_PROCESSED_SUCCESSFULLY: "Webhook processed successfully",
  CHANGE_PASSWORD_SUCCESS: "Successfully changed the password",
  STYKU: {
    BUSINESS_TYPES_CREATED: "Business types has been created successfully!",
    SCAN_SCHEDULE_SUCCESS: "Successfully scheduled a scan",
    VERIFY_PROFILE_SUCCESS: "Successfully verified the profile",
    SEND_VERIFICATION_CODE_SUCCESS: "Successfully sent a verification code",
    VERIFY_CODE_SUCCESS: "Successfully verified a code",
    SECURE_ACCOUNT_SUCCESS: "Successfully secured the profile",
    LOGIN_SUCCESS: "Successfully logged in the profile",
    RESET_PASSWORD_SUCCESS: "Successfully reset the password",
    SIGN_OUT_SUCCESS: "Successfully signed out",
    GET_PROFILE_SUCCESS: "Successfully found the profile details",
  },
  APPSTLE: {
    GET_SUBSCRIPTIONS_SUCCESS: "Successfully found the subscription details",
  },
};
