import { CustomError } from "~/models/error";
import { ErrorHandlingService } from "./ErrorHandlingService";
import { METAFIELD } from "~/utils/shopifyConstants";
import { ERROR_MESSAGES } from "~/utils/stringConstants";

export const logErrorToConsole = (context: string, error?: Error) => {
    console.error(`[${context}]`, error?.message);
};

/**
 * Logs errors to the appropriate handler based on their type and context.
 * 
 * - If the error is a `CustomError`, extracts `ownerType` and `ownerId` from its context and prepares metafield parameters for logging.
 * - If `ownerType` and `ownerId` are defined, includes these details in the error log parameters.
 * - Calls `ErrorHandlingService.handleError` with the extracted details and flags the error as critical.
 * - For non-`CustomError` instances, logs the error with a generic failure context and flags it as critical.
 * 
 * @param {Object} args - The arguments for handling the error.
 * @param {AdminContext} args.admin - The admin context for error logging.
 * @param {Error} args.error - The error to be logged.
 * @param {any} args.payload - Additional payload data for error context.
 */
export async function handleErrorLogging({
    admin,
    error,
    payload,
}: any) {
    if (error instanceof CustomError) {
        const { ownerType, ownerId } = ErrorHandlingService.getOwnerDetails(error.context, payload);

        const params = ownerType && ownerId ? {
            ownerType,
            ownerId,
            namespace: METAFIELD.NAMESPACE.VITAL,
            key: METAFIELD.KEY.ERROR_LOGS,
            type: METAFIELD.TYPE.LIST_METAOBJECT_REFERENCE,
        } : undefined;

        await ErrorHandlingService.handleError({
            admin,
            context: error.name,
            error,
            isCritical: true,
            params,
        });
    } else {
        await ErrorHandlingService.handleError({
            admin,
            context: ERROR_MESSAGES.FAILURE,
            error,
            isCritical: true,
        });
    }
}