version: "3.8"
services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - SHOPIFY_APP_URL=${SHOPIFY_APP_URL}
      - DATABASE_URL=${DATABASE_URL}
      - SHOPIFY_API_KEY=${SHOPIFY_API_KEY}
      - SHOPIFY_API_SECRET=${SHOPIFY_API_SECRET}
      - SCOPES=${SCOPES}
      - APPSTLE_API_KEY=${APPSTLE_API_KEY}
      - VITAL_ENV=${VITAL_ENV}
      - VITAL_API_KEY=${VITAL_API_KEY}
