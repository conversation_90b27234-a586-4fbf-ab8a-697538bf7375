import type { AdminApiContextWithoutRest } from "node_modules/@shopify/shopify-app-remix/dist/ts/server/clients";
import { ErrorSource, ErrorPlatform } from "~/models/enums";
import { CustomError } from "~/models/error";
import type {
    MetafieldDefinitionCreateInput,
    MetafieldDefinitionCreateResponse,
    MetafieldDefinitionNode,
    MetafieldInput,
} from "~/models/shopify/metafield";
import { LIMIT } from "~/utils/shopifyConstants";
import { ERROR_MESSAGES } from "~/utils/stringConstants";

/**
 * Checks if a metafield definition exists for the given namespace, key, and owner type using GraphQL.
 *
 * @param admin - The Shopify Admin API context.
 * @param namespace - The namespace of the metafield definition.
 * @param key - The key of the metafield definition.
 * @param ownerType - The owner type of the metafield definition (e.g., PRODUCT, ORDER).
 * @returns Promise<boolean> - Returns true if the metafield definition exists, false otherwise.
 */
export async function getMetafieldDefinitions(
    admin: AdminApiContextWithoutRest,
    namespace: string,
    key: string,
    ownerType: string,
    limit: number = LIMIT.DEFAULT,
): Promise<MetafieldDefinitionNode | undefined> {
    try {
        const query = `
            query { 
                metafieldDefinitions( ownerType: ${ownerType} first: ${limit} query: "namespace:${namespace} AND key:${key}") {
                    nodes {
                        id
                        name
                        namespace
                        key
                    }
                }
            }`;

        const response = await admin.graphql(query);
        const { data } = await response.json();

        return data.metafieldDefinitions.nodes[0];
    } catch (error) {
        throw new CustomError(
            `${ERROR_MESSAGES.SHOPIFY.GET_METAFIELD} namespace: "${namespace}", key: "${key}", and ownerType: "${ownerType}".`,
            ErrorSource.Metafield,
            ErrorPlatform.Shopify,
            error,
        );
    }
}

/**
 * Creates a metafield definition in Shopify using the provided payload.
 *
 * @param {AdminApiContextWithoutRest} admin - The admin API context to interact with Shopify.
 * @param {MetafieldDefinitionCreateInput} metafieldDefinitionPayload - The payload containing the metafield definition details.
 * @returns {Promise<MetafieldDefinitionCreateResponse>} - A promise that resolves when the metafield definition is created.
 */
export async function createMetafieldDefinition(
    admin: AdminApiContextWithoutRest,
    metafieldDefinitionPayload: MetafieldDefinitionCreateInput,
): Promise<MetafieldDefinitionCreateResponse | undefined> {
    const { name, namespace, key, type, ownerType, validations } = metafieldDefinitionPayload;

    try {
        const response = await admin.graphql(
            `#graphql
          mutation CreateMetafieldDefinition($definition: MetafieldDefinitionInput!) {
            metafieldDefinitionCreate(definition: $definition) {
              createdDefinition {
                id
                name
              }
              userErrors {
                field
                message
              }
            }
          }`,
            {
                variables: {
                    definition: {
                        name,
                        namespace,
                        key,
                        type,
                        ownerType,
                        ...(validations && { validations }),
                    },
                },
            },
        );

        const { data } = await response.json();
        const result = data?.metafieldDefinitionCreate;

        // Check for user errors in the GraphQL response
        if (result?.userErrors?.length) {
            throw new CustomError(
                `${ERROR_MESSAGES.SHOPIFY.CREATE_METAFIELD_DEFINITION} name: "${name}", key: "${key}", and ownerType: "${ownerType}".`,
                ErrorSource.MetafieldDefinition,
                ErrorPlatform.Shopify,
                result?.userErrors,
            );
        }

        return data.metafieldDefinitionCreate.createdDefinition;
    } catch (error) {
        if (error instanceof CustomError) {
            throw error;
        }
        throw new CustomError(
            `${ERROR_MESSAGES.FAILURE} name: "${name}", key: "${key}", and ownerType: "${ownerType}".`,
            ErrorSource.MetafieldDefinition,
            ErrorPlatform.Shopify,
            error,
        );
    }
}

// Update meta-field
export async function updateMetafield(
    admin: AdminApiContextWithoutRest,
    metafieldData: MetafieldInput,
) {
    const { ownerId, namespace, type, key, value } = metafieldData;

    try {
        const responseAwaiter = await admin.graphql(
            `#graphql
            mutation metafieldsSet($metafields: [MetafieldsSetInput!]!) {
                metafieldsSet(metafields: $metafields) {
                    metafields {
                        key
                        value 
                    }
                    userErrors {
                        field
                        message
                    }
                }
            }`,
            {
                variables: {
                    metafields: [
                        {
                            ownerId,
                            namespace,
                            type,
                            key,
                            value,
                        },
                    ],
                },
            },
        );

        const response = await responseAwaiter.json();

        const result = response.data?.metafieldsSet;

        if (result?.userErrors?.length > 0) {
            throw new CustomError(
                `${ERROR_MESSAGES.SHOPIFY.UPDATE_METAFIELD}: ${JSON.stringify(result.userErrors)}.`,
                ErrorSource.Metafield,
                ErrorPlatform.Shopify,
                result?.userErrors,
            );
        }

        return result?.metafields;
    } catch (error) {
        if (error instanceof CustomError) {
            throw error;
        }
        throw new CustomError(
            `${ERROR_MESSAGES.FAILURE} ownerId: "${ownerId}", key: "${key}", and value: "${value}".`,
            ErrorSource.Metafield,
            ErrorPlatform.Shopify,
            error,
        );
    }
}
