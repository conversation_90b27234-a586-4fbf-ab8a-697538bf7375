import type APIPayload from "~/models/apiPayload";
import type Save from "~/models/save";
import type { Email, EmailCode, EmailPassword, Profile, ProfilePassword } from "~/models/styku/profile";
import type APIResponse from "~/models/apiResponse";
import type { LoginRequestPayload, LoginResponse } from "~/models/styku/login";
import { API_PAYLOADS } from "~/utils/apiConstants";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "~/utils/stringConstants";
import { makeRequest } from "../fetch";

/**
 * Verify a profile email
 * @param email Email
 * @returns API response with status, message and data
 */
export async function verifyProfile(email: string): Promise<APIResponse<boolean>> {
  const apiPayload: APIPayload<Email> = API_PAYLOADS.verifyProfile;
  apiPayload.body = { email };
  apiPayload.successMessage = SUCCESS_MESSAGES.STYKU.VERIFY_PROFILE_SUCCESS;
  apiPayload.failureMessage = ERROR_MESSAGES.STYKU.VERIFY_PROFILE;

  return await makeRequest<Email, boolean>(apiPayload);
}

/**
 * Send an email verification code (For the resend functionality)
 * @param email Email
 * @returns API response with status, message and data
 */
export async function sendVerificationCode(email: string): Promise<APIResponse<boolean>> {
  const apiPayload: APIPayload<Email> = API_PAYLOADS.sendVerificationCode;
  apiPayload.body = { email };
  apiPayload.successMessage = SUCCESS_MESSAGES.STYKU.SEND_VERIFICATION_CODE_SUCCESS;
  apiPayload.failureMessage = ERROR_MESSAGES.STYKU.SEND_VERIFICATION_CODE;

  return await makeRequest<Email, boolean>(apiPayload);
}

/**
 * Verify a code
 * @param email Email
 * @param code Verification code
 * @returns API response with status, message and data
 */
export async function verifyCode(email: string, code: string): Promise<APIResponse<Profile>> {
  const apiPayload: APIPayload<EmailCode> = API_PAYLOADS.verifyCode;
  apiPayload.body = { email, code };
  apiPayload.successMessage = SUCCESS_MESSAGES.STYKU.VERIFY_CODE_SUCCESS;
  apiPayload.failureMessage = ERROR_MESSAGES.STYKU.VERIFY_CODE;

  return await makeRequest<EmailCode, Profile>(apiPayload);
}

/**
 * Secure profile account
 * @param profile Profile details
 * @returns API response with status, message and data
 */
export async function secureAccount(profile: ProfilePassword): Promise<APIResponse<boolean>> {
  const apiPayload: APIPayload<ProfilePassword> = API_PAYLOADS.secureAccount;
  apiPayload.body = profile;
  apiPayload.successMessage = SUCCESS_MESSAGES.STYKU.SECURE_ACCOUNT_SUCCESS;
  apiPayload.failureMessage = ERROR_MESSAGES.STYKU.SECURE_ACCOUNT;

  return await makeRequest<ProfilePassword, boolean>(apiPayload);
}

/**
 * Login profile account
 * @param payload Username and password
 * @returns API response with status, message and data
 */
export async function login(payload: LoginRequestPayload): Promise<APIResponse<LoginResponse>> {
  const apiPayload: APIPayload<LoginRequestPayload> = API_PAYLOADS.login;
  apiPayload.body = payload;
  apiPayload.successMessage = SUCCESS_MESSAGES.STYKU.LOGIN_SUCCESS;
  apiPayload.failureMessage = ERROR_MESSAGES.STYKU.LOGIN;

  return await makeRequest<LoginRequestPayload, LoginResponse>(apiPayload);
}

/**
 * Reset password
 * @param payload Email, password, and confirm password
 * @returns API response with status, message and data
 */
export async function resetPassword(payload: EmailPassword): Promise<APIResponse<Save>> {
  const apiPayload: APIPayload<EmailPassword> = API_PAYLOADS.resetPassword;
  apiPayload.body = payload;
  apiPayload.successMessage = SUCCESS_MESSAGES.STYKU.RESET_PASSWORD_SUCCESS;
  apiPayload.failureMessage = ERROR_MESSAGES.STYKU.RESET_PASSWORD;

  return await makeRequest<EmailPassword, Save>(apiPayload);
}

/**
 * Sign out
 * @param authToken Signed in user auth token
 * @returns API response with status, message and data
 */
export async function signOut(authToken: string | null): Promise<APIResponse<boolean>> {
  const apiPayload: APIPayload<null> = API_PAYLOADS.signOut;
  apiPayload.addAuthHeader(authToken);
  apiPayload.successMessage = SUCCESS_MESSAGES.STYKU.SIGN_OUT_SUCCESS;
  apiPayload.failureMessage = ERROR_MESSAGES.STYKU.SIGN_OUT;

  return await makeRequest<null, boolean>(apiPayload);
}
