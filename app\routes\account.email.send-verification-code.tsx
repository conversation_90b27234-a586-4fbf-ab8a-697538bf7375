import { json, type ActionFunctionArgs } from "@remix-run/node";
import { sendVerificationCode } from "~/api/styku/account";
import type APIResponse from "~/models/apiResponse";
import { authenticate } from "~/shopify.server";

/**
 * Send email verification code
 * Returns:
 * 1. 200 - Successfully sent a verification code
 * 2. 601 - Invalid email address
 * 3. 603 - Unable to send an email code
 * 4. 500 - Other errors
 */
export async function action({ request }: ActionFunctionArgs) {
  await authenticate.public.appProxy(request);

  const { email } = await request.json();
  const response: APIResponse<boolean> = await sendVerificationCode(email);
  return json(response);
}
