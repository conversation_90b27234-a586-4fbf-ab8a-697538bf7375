import VitalClientService from "./vitalClient";

import type {
    ClientFacingUser,
    ClientFacingUserKey,
    UserCreateBody,
} from "@tryvital/vital-node/api";
import { VitalError } from "@tryvital/vital-node";
import { CustomError } from "~/models/error";
import { ERROR_MESSAGES } from "~/utils/stringConstants";
import { ErrorSource, ErrorPlatform } from "~/models/enums";
import { retryOnError } from "~/utils/retryOnError";
import { RETRY_OPTION, STATUS_CODE } from "~/utils/vitalConstants";

/**
 * Creates a new user with the given clientUserId.
 * @param clientUserId - A unique identifier for the user to be created.
 * @returns The created user's key or undefined if an error occurs.
 */
export async function createUser(
    clientUserId: string,
): Promise<ClientFacingUserKey | undefined> {
    try {
        const client = await VitalClientService.getInstance();
        const userCreateBody: UserCreateBody = { clientUserId };

        return await retryOnError(
            async () => {
                return await client.user.create(userCreateBody);
            },
            [STATUS_CODE.requestTimeout, STATUS_CODE.conflict, STATUS_CODE.manyRequests, STATUS_CODE.internalServerError],
            { retries: RETRY_OPTION.retries, delay: RETRY_OPTION.delay }
        );
    } catch (error) {
        throw new CustomError(
            `${ERROR_MESSAGES.VITAL.CREATE_USER}: ${clientUserId}`,
            ErrorSource.User,
            ErrorPlatform.Vital,
            error,
        );
    }
}

/**
 * Retrieves a user by their clientUserId.
 * @param clientUserId - A unique identifier for the user to be retrieved.
 * @returns The user details or undefined if an error occurs.
 */
export async function getUserByClientUserId(
    clientUserId: string,
): Promise<ClientFacingUser | undefined> {
    try {
        const client = await VitalClientService.getInstance();

        return await client.user.getByClientUserId(clientUserId);
    } catch (error) {
        if (error instanceof VitalError) {
            switch (error.statusCode) {
                case 404:
                    return undefined;
                default:
                    throw new CustomError(
                        `${ERROR_MESSAGES.VITAL.GET_USER}: "${clientUserId}"`,
                        ErrorSource.User,
                        ErrorPlatform.Vital,
                        error,
                    );
            }
        } else {
            throw new CustomError(
                `${ERROR_MESSAGES.FAILURE}: ${clientUserId}`,
                ErrorSource.User,
                ErrorPlatform.Vital,
                error,
            );
        }
    }
}
