import { useEffect } from "react";
import { json, use<PERSON><PERSON>cher, use<PERSON>oaderD<PERSON> } from "@remix-run/react";
import { useAppBridge } from "@shopify/app-bridge-react";
import { authenticate } from "~/shopify.server";
import { getLabTests } from "~/api/vital/labTest";
import { getProducts } from "~/api/shopify/graphQL/product";
import { ProductTable } from "~/components/ProductTable";
import { Button, Page, PageActions } from "@shopify/polaris";
import { createProductAndUpdateVariant } from "~/services/shopify/productService";
import EmptyStatePlaceholder from "~/components/EmptyStatePlaceholder";
import { EMPTY_STATE_STRING } from "~/utils/stringConstants";
import type { ClientFacingLabTest } from "@tryvital/vital-node/api";
import type { ProductInput } from "~/models/shopify/productInput";

import type {
  ActionFunctionArgs,
  LoaderFunctionArgs,
  TypedResponse,
} from "@remix-run/node";
import {
  LIMIT,
  METAFIELD,
  PRODUCT_TYPE,
  PRODUCT_VENDOR,
  STATUS,
} from "~/utils/shopifyConstants";
import type {
  Product,
  ProductsResponse,
  SyncProductsResponse,
} from "~/models/shopify/productResponse";

export async function loader({
  request,
}: LoaderFunctionArgs): Promise<TypedResponse<Product[]>> {
  const { admin } = await authenticate.admin(request);

  const existingProducts: ProductsResponse = await getProducts(
    admin,
    LIMIT.MAX,
    PRODUCT_TYPE.HEALTH_PRODUCTS,
    PRODUCT_VENDOR.VITAL,
    METAFIELD.NAMESPACE.VITAL,
    METAFIELD.KEY.LAB_TEST_ID,
  );

  const products: Product[] = existingProducts.edges.map((edge) => edge.node);

  return json(products);
}

export const action = async ({ request }: ActionFunctionArgs) => {
  try {
    const { admin } = await authenticate.admin(request);

    const labTests: ClientFacingLabTest[] = await getLabTests();

    // Query existing products in Shopify with the metafield
    const existingProducts: ProductsResponse = await getProducts(
      admin,
      LIMIT.MAX,
      PRODUCT_TYPE.HEALTH_PRODUCTS,
      PRODUCT_VENDOR.VITAL,
      METAFIELD.NAMESPACE.VITAL,
      METAFIELD.KEY.LAB_TEST_ID,
    );

    const products: Product[] = existingProducts.edges.map((edge) => edge.node);

    const newProducts: ClientFacingLabTest[] = [];

    await Promise.all(
      labTests.map(async (labTest) => {
        const existingProduct = products.find(
          (product) => product.metafield.value === `${labTest.id}`,
        );

        if (!existingProduct) {
          const product: ProductInput = {
            title: labTest.name,
            handle: labTest.slug,
            price: labTest.price.toString(),
            vendor: PRODUCT_VENDOR.VITAL,
            productType: PRODUCT_TYPE.HEALTH_PRODUCTS,
            status: STATUS.ACTIVE,
            metafields: {
              namespace: METAFIELD.NAMESPACE.VITAL,
              key: METAFIELD.KEY.LAB_TEST_ID,
              value: labTest.id,
              type: METAFIELD.TYPE.SINGLE_LINE_TEXT_FIELD,
            },
          };

          await createProductAndUpdateVariant(admin, product);
          newProducts.push(labTest);
        }
      }),
    );

    return json({ existingProducts, newProducts });
  } catch (error) {
    console.error("Error creating products:", error);
    throw new Response("Failed to create products", { status: 500 });
  }
};

export default function SyncProducts() {
  const products: Product[] = useLoaderData<typeof loader>();

  const fetcher = useFetcher();
  const shopify = useAppBridge();

  const isLoading =
    ["loading", "submitting"].includes(fetcher.state) &&
    fetcher.formMethod === "POST";

  useEffect(() => {
    if (fetcher.data) {
      const { newProducts } = fetcher.data as SyncProductsResponse;
      if (newProducts && newProducts.length > 0) {
        shopify.toast.show(
          `Added ${newProducts.length} new products to Shopify`,
        );
      } else {
        shopify.toast.show("All products are up-to-date with Vital");
      }
    }
  }, [fetcher.data, shopify]);

  const generateProducts = () => fetcher.submit({}, { method: "POST" });

  return (
    <Page fullWidth>
      <PageActions
        primaryAction={
          <Button
            loading={isLoading}
            tone="critical"
            size="large"
            onClick={generateProducts}
          >
            {EMPTY_STATE_STRING.SYNC_PRODUCT.buttonText}
          </Button>
        }
      />
      {products.length > 0 ? (
        <ProductTable products={products} />
      ) : (
        <EmptyStatePlaceholder
          heading={EMPTY_STATE_STRING.SYNC_PRODUCT.heading}
          message={EMPTY_STATE_STRING.SYNC_PRODUCT.message}
          action={{
            content: EMPTY_STATE_STRING.SYNC_PRODUCT.buttonText,
          }}
        />
      )}
    </Page>
  );
}
