import type { AdminApiContextWithoutRest } from "node_modules/@shopify/shopify-app-remix/dist/ts/server/clients";
import type { ActionFunctionArgs } from "@remix-run/node";
import { authenticate } from "../shopify.server";
import { STATUS_CODE } from "~/utils/vitalConstants";
import db from "../db.server";
import type { LineItem } from "~/models/shopify/webhook/order";
import { webhookQueue } from "~/queue/orderQueue";
import { getProduct } from "~/api/shopify/graphQL/product";
import { graphQLUtils } from "~/api/shopify/graphQL/utility";
import { OWNER_TYPE } from "~/utils/shopifyConstants";
import { toSnakeCase } from "~/utils/stringUtils";

export const action = async ({ request }: ActionFunctionArgs) => {
  try {
    const { topic, admin, session, payload, webhookId } = await authenticate.webhook(request);

    if (!admin) {
      return new Response(null, { status: STATUS_CODE.unuthorized });
    }

    if (topic === "ORDERS_CREATE" && session) {
      // Fire and forget background task
      setImmediate(async () => {
        try {
          await saveWebhookTask(webhookId, topic, payload);

          const categorizedProducts = await categorizeProducts(admin, payload.line_items);

          queueCategorizedProducts(categorizedProducts, admin, payload.id.toString());
        } catch (err) {
          console.error("Error in webhook processing:", err);
          await logProcessingError(payload.id.toString(), err);
        }
      });
    }

    return new Response(null, { status: STATUS_CODE.success });
  } catch (error) {
    console.error("Webhook handling error:", error);
    return new Response("Internal Server Error", {
      status: STATUS_CODE.internalServerError,
    });
  }
};


// Save webhook data to the DB
async function saveWebhookTask(webhookId: string, topic: string, payload: Record<string, any>) {
  return db.ordersWebhookTask.create({
    data: {
      id: payload.id,
      webhook_id: webhookId,
      topic,
      payload,
      updated_at: new Date(),
      created_at: new Date(payload.created_at),
    },
  });
}

// Categorize line items into vendor_productType groups
async function categorizeProducts(admin: any, items: LineItem[]) {
  const result: Record<string, LineItem[]> = {};

  for (const item of items) {
    const product = await getProduct(
      admin,
      graphQLUtils.createGraphQLId(OWNER_TYPE.PRODUCT, item.product_id),
      "vital",
      "lab_test_id"
    );

    const vendorKey = toSnakeCase(item.vendor);
    const productTypeKey = toSnakeCase(product.productType);
    const categoryKey = `${vendorKey}_${productTypeKey}`;

    const enrichedItem = {
      ...item,
      product_type: product.productType,
      category_key: categoryKey,
    };

    if (!result[categoryKey]) {
      result[categoryKey] = [];
    }

    result[categoryKey].push(enrichedItem);
  }

  return result;
}

// Push categorized product groups to processing queue
function queueCategorizedProducts(
  categorizedProducts: Record<string, LineItem[]>,
  admin: AdminApiContextWithoutRest,
  orderId: string
) {
  for (const [items] of Object.entries(categorizedProducts)) {
    webhookQueue.push({ payload: items, admin, orderId });
  }
}

// Handle webhook processing errors
async function logProcessingError(orderId: string, error: unknown) {
  await db.orderProcessingError.create({
    data: {
      orderId,
      productId: "WEBHOOK_PROCESSING",
      productTitle: "Webhook Processing",
      quantity: 1,
      failedQuantity: 1,
      errorMessage: error instanceof Error ? error.message : "Unknown error",
    },
  });
}