import type APIPayload from "~/models/apiPayload";
import type APIResponse from "~/models/apiResponse";
import { HEADERS } from "~/utils/apiConstants";

/**
 * Make request to the given data
 */
export async function makeRequest<TBody, TResponse>(apiPayload: APIPayload<TBody>): Promise<APIResponse<TResponse>> {
  let data: TResponse;

  try {
    const response = await fetch(apiPayload.endpoint, {
      method: apiPayload.method,
      headers: apiPayload.headers,
      body: apiPayload.body ? JSON.stringify(apiPayload.body) : null,
    });

    // Handle success response
    if (response.ok) {
      const contentType = response.headers.get(HEADERS.CONTENT_TYPE);

      if (contentType?.includes(HEADERS.CONTENT_TEXT_PLAIN)) {
        data = (await response.text()) as TResponse;
      } else {
        data = await response.json();
      }

      return { status: response.status, message: apiPayload.successMessage, data };
    } else {
      // Handle failure response
      const message = `${response.status}. ${apiPayload.failureMessage}. ${await response.text()}`;
      console.error(message);
      return { status: response.status, message, data: null };
    }
  } catch (error) {
    // Handle error
    const message = `${apiPayload.failureMessage}. ${error}`;
    console.error(message);
    return { status: 500, message, data: null };
  }
}
