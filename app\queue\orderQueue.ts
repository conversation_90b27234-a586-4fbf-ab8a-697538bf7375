import BetterQueue from 'better-queue';
import { processQueuedOrder } from '~/services/shopify/orderProcessor';
import db from '~/db.server';
import type { QueueStats, QueueTask } from '~/models/queueTask';

export const webhookQueue = new BetterQueue(
  async (task: QueueTask, cb) => {
    const { payload, admin, orderId, vendor, productType } = task;
    console.log(`Processing order ${orderId} for vendor ${vendor} and product type ${productType}`);
    
    try {
      await processQueuedOrder(admin, payload, orderId);
      cb(null, { success: true });
    } catch (err) {
      console.error(`Error processing order ${orderId}:`, err);
      
      // Update webhook task status
      await db.ordersWebhookTask.update({
        where: { id: BigInt(orderId) },
        data: {
          status: 'failed',
          last_error: err instanceof Error ? err.message : 'Unknown error',
          updated_at: new Date(),
        },
      });
      
      cb(err);
    }
  },
  {
    concurrent: 4, // Process up to 4 orders at a time
    maxRetries: 3,
    retryDelay: 5000, // 5 seconds between retries
  }
);

webhookQueue.on('task_failed', (taskId: string, err: Error, stats: QueueStats) => {
  console.error(`Task ${taskId} failed after ${stats.attempts} attempts:`, err);
  // You could implement additional error handling here
  // For example, sending notifications or logging to an error tracking service
});

webhookQueue.on('task_finish', (taskId: string, result: { success: boolean }, stats: QueueStats) => {
  console.log(`Task ${taskId} completed in ${stats.elapsed}ms`);
  // You could implement additional success handling here
  // For example, sending notifications or updating other systems
});