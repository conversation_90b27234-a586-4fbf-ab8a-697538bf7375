import {
  VitalOrdersMetaobject,
  type ShopifyMetaobjectUpsertInput,
} from "~/models/shopify/metaobject";
import type {
  LineItem,
  OrderResponse,
  ShippingAddress,
} from "~/models/shopify/webhook/order";
import type { AdminApiContextWithoutRest } from "node_modules/@shopify/shopify-app-remix/dist/ts/server/clients";
import type { MetafieldInput } from "~/models/shopify/metafield";
import { getProduct } from "../../api/shopify/graphQL/product";
import { getCustomerWithMetafields } from "~/api/shopify/graphQL/customer";
import { updateMetafield } from "~/api/shopify/graphQL/metafields";
import { createRegistrableTestkitOrder } from "~/api/vital/testKits";
import { createUser, getUserByClientUserId } from "~/api/vital/user";
import { upsertMetaobject } from "~/api/shopify/graphQL/metaObject";
import { graphQLUtils } from "~/api/shopify/graphQL/utility";
import { CustomError } from "~/models/error";
import { handleErrorLogging } from "../loggingService";
import { processRefund } from "./processRefundServices";
import { ErrorPlatform, ErrorSource } from "~/models/enums";

import type {
  ClientFacingOrder,
  ClientFacingUser,
  ClientFacingUserKey,
  CreateRegistrableTestkitOrderRequest,
  PostOrderResponse,
} from "@tryvital/vital-node/api";
import {
  METAFIELD,
  OWNER_TYPE,
  PRODUCT_TITLE,
  PRODUCT_TYPE,
  PRODUCT_VENDOR,
  VITAL_ORDERS_META_DEFINITION,
} from "~/utils/shopifyConstants";
import type { Customer } from "~/models/shopify/customer";

/**
 * Processes order products based on their types and updates customer metafields.
 *
 * @param admin - The admin context to interact with Shopify APIs.
 * @param payload - The payload containing order details.
 */
export async function processOrderProducts(
  admin: AdminApiContextWithoutRest,
  payload: OrderResponse,
) {
  try {
    const shopifyCustomer: Customer | undefined =
      await getCustomerWithMetafields(
        admin,
        payload.customer.admin_graphql_api_id,
      );

    if (!shopifyCustomer) {
      throw new Error(
        `Shopify customer is not found with the id ${payload.customer.admin_graphql_api_id}`,
      );
    }

    const failedLineItemsIds: number[] = [];

    const metaObjectIds: (string | undefined)[] = [];
    let user: ClientFacingUserKey | undefined;
    const isExistsHealthProducts = payload.line_items.some(
      (item) => item.vendor === PRODUCT_VENDOR.VITAL,
    );

    if (isExistsHealthProducts) {
      try {
        user = await syncUserAndUpdateMetafield(admin, shopifyCustomer);
      } catch (error) {
        if (
          error instanceof CustomError &&
          error.context === ErrorSource.User &&
          error.source == ErrorPlatform.Vital
        ) {
          // Processes a refund for health products in an order
          await processRefund(admin, payload);

          // Handles error logging by differentiating CustomError with contextual and logging other errors with a generic failure context
          await handleErrorLogging({ admin, error, payload });
        }
      }

      const tasks = payload.line_items.map(async (item) => {
        try {
          await processLineItem(
            admin,
            item,
            user,
            payload,
            metaObjectIds,
            shopifyCustomer,
          );
        } catch (error) {
          if (
            error instanceof CustomError &&
            error.context === ErrorSource.Testkit &&
            error.source == ErrorPlatform.Vital
          ) {
            failedLineItemsIds.push(item.id);
          }

          await handleErrorLogging({ admin, error, payload });
        }
      });

      await Promise.allSettled(tasks);

      if (failedLineItemsIds.length > 0) {
        const failedLineItems = payload.line_items.filter((item) =>
          failedLineItemsIds.includes(item.id),
        );

        await processRefund(admin, {
          ...payload,
          line_items: failedLineItems,
        });
      }

      if (metaObjectIds.length > 0) {
        await updateMetafield(admin, {
          ownerId: graphQLUtils.createGraphQLId(OWNER_TYPE.ORDER, payload.id),
          namespace: METAFIELD.NAMESPACE.VITAL,
          type: METAFIELD.TYPE.LIST_METAOBJECT_REFERENCE,
          key: METAFIELD.KEY.VITAL_ORDERS,
          value: JSON.stringify(metaObjectIds),
        });
      }
    }
  } catch (error) {
    await handleErrorLogging({ admin, error, payload });
  }
}

/**
 * Processes a line item based on its product type.
 *
 * - Fetches product details using the product ID and relevant metafields.
 * - For "Pay Per Scan" products, delegates processing to `processPayPerScanProduct`.
 * - For "Health Products," iterates through the item quantity to process each unit, creating and storing metaobject IDs if available.
 *
 * @param {AdminApiContextWithRest<ShopifyRestResources>} admin - Admin API context for Shopify operations.
 * @param {LineItem} item - The line item to process.
 * @param {ClientFacingUserKey | undefined} user - User details for context (if available).
 * @param {OrderResponse} payload - Order data associated with the line item.
 * @param {(string | undefined)[]} metaObjectIds - Array to store metaobject IDs created during processing.
 */
async function processLineItem(
  admin: AdminApiContextWithoutRest,
  item: LineItem,
  user: ClientFacingUserKey | undefined,
  payload: OrderResponse,
  metaObjectIds: (string | undefined)[],
  shopifyCustomer: Customer,
) {
  const product = await getProduct(
    admin,
    graphQLUtils.createGraphQLId(OWNER_TYPE.PRODUCT, item.product_id),
    METAFIELD.NAMESPACE.VITAL,
    METAFIELD.KEY.LAB_TEST_ID,
  );

  switch (product.productType) {
    case PRODUCT_TYPE.HEALTH_PASS:
      await processPayPerScanProduct(admin, payload, shopifyCustomer);
      break;
    case PRODUCT_TYPE.HEALTH_PRODUCTS:
      const labTestId: string = product.metafield.value;

      if (user?.userId) {
        for (let i = 0; i < item.quantity; i++) {
          const metaObjectId = await processHealthProducts(
            admin,
            labTestId,
            user?.userId,
            payload,
          );
          if (metaObjectId) metaObjectIds.push(metaObjectId);
        }
      }
      break;
  }
}

/**
 * Processes "Pay Per Scan" product type and updates customer metafields.
 *
 * @param admin - The admin context to interact with Shopify APIs.
 * @param payload - The payload containing order details.
 */
async function processPayPerScanProduct(
  admin: AdminApiContextWithoutRest,
  payload: OrderResponse,
  shopifyCustomer: Customer,
) {
  // Retrieve current bundles count from customer metafields
  const currentPayPerScanBundle: number = parseInt(
    shopifyCustomer.totalPPSMetafield?.value ?? "0",
  );

  // Find the "Pay Per Scan" product line item
  const payPerScanProduct: LineItem | undefined = payload.line_items.find(
    (line_item: LineItem) => line_item.title === PRODUCT_TITLE.HEALTH_PASS,
  );

  if (payPerScanProduct) {
    const additionalBundles = parseInt(payPerScanProduct.variant_title, 10);
    const updatedPayPerScanBundle = currentPayPerScanBundle + additionalBundles;

    const metafieldData: MetafieldInput = {
      ownerId: shopifyCustomer.id,
      namespace: METAFIELD.NAMESPACE.STYKU,
      type: METAFIELD.TYPE.NUMBER_INTEGER,
      key: METAFIELD.KEY.AVAILABLE_SCAN_BUNDLES,
      value: updatedPayPerScanBundle.toString(),
    };

    await updateMetafield(admin, metafieldData);
  }
}

/**
 * Processes health product orders by creating a registrable test kit order in Vital and syncing it with Shopify.
 *
 * - Extracts shipping and order details from the payload.
 * - Constructs the `CreateRegistrableTestkitOrderRequest` payload for Vital's API.
 * - Calls Vital's API to create the registrable test kit order.
 * - If the order is successfully created, generates a Shopify metaobject using `VitalOrdersMetaobject`.
 * - Upserts the metaobject into Shopify and returns the metaobject ID.
 *
 * @param {AdminApiContextWithRest<ShopifyRestResources>} admin - Admin API context for Shopify operations.
 * @param {string} labTestId - The ID of the lab test associated with the health product.
 * @param {string} userId - The ID of the user placing the order.
 * @param {OrderResponse} payload - Shopify order response containing shipping and order details.
 * @returns {Promise<string | undefined>} - The metaobject ID if created successfully, otherwise undefined.
 */
async function processHealthProducts(
  admin: AdminApiContextWithoutRest,
  labTestId: string,
  userId: string,
  payload: OrderResponse,
) {
  const { shipping_address } = payload;
  const shopifyOrderId = payload.id;

  const {
    name,
    address1,
    phone,
    city,
    country,
    zip,
    address2,
    province,
  }: ShippingAddress = shipping_address;

  const orderCreatePayload: CreateRegistrableTestkitOrderRequest = {
    userId: userId,
    labTestId: labTestId,
    shippingDetails: {
      receiverName: name,
      firstLine: address1,
      secondLine: address2,
      city: city,
      state: province,
      zip: zip,
      country: country,
      phoneNumber: phone,
    },
  };

  // Create registrable kit order in Vital
  const response: PostOrderResponse | undefined =
    await createRegistrableTestkitOrder(orderCreatePayload);

  if (response) {
    const order: ClientFacingOrder | undefined = response?.order;

    const metaobject: ShopifyMetaobjectUpsertInput =
      VitalOrdersMetaobject.create(order, shopifyOrderId);

    // Create metaobject entry when order is created in Vital
    const metaObjectId = await upsertMetaobject(
      admin,
      VITAL_ORDERS_META_DEFINITION.type,
      response.order.id.toString(),
      metaobject,
    );
    return metaObjectId;
  }
}

/**
 * Synchronizes a user by creating a new user in Vital if not already present, and updates the Shopify customer metafield
 *
 * @param admin - The admin context to interact with Shopify APIs
 * @param customerId - The ID of the customer to sync
 * @returns The user object, or undefined if user creation fails
 */
export async function syncUserAndUpdateMetafield(
  admin: AdminApiContextWithoutRest,
  shopifyCustomer: Customer,
): Promise<ClientFacingUser | undefined> {
  // Retrieve profile id metafield
  const profileId: string =
    shopifyCustomer.stykuProfileIdMetafield?.value || "";

  let existingUser: ClientFacingUser | undefined =
    await getUserByClientUserId(profileId);

  if (!existingUser) {
    existingUser = (await createUser(profileId)) as ClientFacingUser;

    // Prepare and update metafield data with the new user ID
    const metafieldData: MetafieldInput = {
      ownerId: shopifyCustomer.id,
      namespace: METAFIELD.NAMESPACE.VITAL,
      type: METAFIELD.TYPE.SINGLE_LINE_TEXT_FIELD,
      key: METAFIELD.KEY.USER_ID,
      value: existingUser?.userId ?? "",
    };

    await updateMetafield(admin, metafieldData);
  }
  return existingUser;
}
