import { IndexTable, Link, Text, useBreakpoints } from "@shopify/polaris";
import type { Product } from "~/models/shopify/productResponse";

interface ProductTableProps {
  products: Product[];
}

export function ProductTable({ products }: ProductTableProps) {
  const { smDown } = useBreakpoints();
  const rowMarkup = products.map((product, index) => (
    <IndexTable.Row id={product.id} key={product.id} position={index}>
      <IndexTable.Cell>{index + 1}</IndexTable.Cell>
      <IndexTable.Cell>
        <Text variant="bodyMd" fontWeight="bold" as="span">
          {product.title}
        </Text>
      </IndexTable.Cell>
      <IndexTable.Cell>{product.variants.edges[0].node.price}</IndexTable.Cell>
      <IndexTable.Cell>{product.metafield.value}</IndexTable.Cell>
      <IndexTable.Cell>
        <Text as="span">{product.productType}</Text>
      </IndexTable.Cell>
      <IndexTable.Cell>
        <Link
          target="_self"
          url={`shopify:admin/products/${product.id.replace("gid://shopify/Product/", "")}`}
        >
          Edit
        </Link>
      </IndexTable.Cell>
    </IndexTable.Row>
  ));
  return (
    <IndexTable
      condensed={smDown}
      itemCount={products.length}
      headings={[
        { title: "No" },
        { title: "Title" },
        { title: "Price" },
        { title: "Vital Lab Test Id" },
        { title: "Product Type" },
        { title: "Actions" },
      ]}
      selectable={false}
    >
      {rowMarkup}
    </IndexTable>
  );
}
