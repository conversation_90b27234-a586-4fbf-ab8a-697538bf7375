import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { getCustomerSubscriptions } from "~/api/appstle/subscription";
import { authenticate } from "~/shopify.server";
import { SHOPIFY_QUERY_PARAMS_KEY } from "~/utils/shopifyConstants";
import type { SubscriptionDetails } from "~/models/appstle/subscription";
import type APIResponse from "~/models/apiResponse";

export async function loader({ request }: LoaderFunctionArgs) {
  const { searchParams } = new URL(request.url);

  const loggedInCustomerId: string | null = searchParams.get(
    SHOPIFY_QUERY_PARAMS_KEY.loggedInCustomerId,
  );

  try {
    await authenticate.public.appProxy(request);
    const subscriptionsResponse: APIResponse<SubscriptionDetails[]> =
      await getCustomerSubscriptions(loggedInCustomerId);

    const subscriptions = subscriptionsResponse.data!.map(
      (subscription: SubscriptionDetails) => {
        const extractedSubscriptions = {
          id: subscription.id,
          status: subscription.status,
          activatedOn: subscription.activatedOn,
          createdAt: subscription.createdAt,
          updatedAt: subscription.updatedAt,
          cancelledOn: subscription.cancelledOn,
          orderAmount: subscription.orderAmount,
          nextBillingDate: subscription.nextBillingDate,
          lastSuccessfulOrder: subscription.lastSuccessfulOrder,
          contractDetailsJSON: subscription.contractDetailsJSON,
        };
        return extractedSubscriptions;
      },
    );

    return json(subscriptions);
  } catch (error) {
    console.error(error);
    throw error;
  }
}
