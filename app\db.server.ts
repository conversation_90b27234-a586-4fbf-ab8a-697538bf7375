import postgres from 'pg'
import { PrismaClient } from "@prisma/client";
import { PrismaPg } from '@prisma/adapter-pg';
const { Pool } = postgres;

const connectionString = `${process.env.DATABASE_URL}`

declare global {
    var prisma: PrismaClient;
}

const pool = new Pool({ connectionString })
const adapter = new PrismaPg(pool)

const prisma: PrismaClient = global.prisma || new PrismaClient({ adapter });

if (process.env.NODE_ENV !== "production") {
    if (!global.prisma) {
        global.prisma = new PrismaClient({ adapter });
    }
}
export default prisma;
