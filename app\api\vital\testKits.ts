import VitalClientService from "./vitalClient";
import type {
    CreateRegistrableTestkitOrderRequest,
    PostOrderResponse,
} from "@tryvital/vital-node/api";
import { VitalError } from "@tryvital/vital-node";
import { CustomError } from "~/models/error";
import { ERROR_MESSAGES } from "~/utils/stringConstants";
import { ErrorSource, ErrorPlatform } from "~/models/enums";
import { RETRY_OPTION, STATUS_CODE } from "~/utils/vitalConstants";
import { retryOnError } from "~/utils/retryOnError";

export async function createRegistrableTestkitOrder(
    orderCreatePayload: CreateRegistrableTestkitOrderRequest,
): Promise<PostOrderResponse | undefined> {
    try {
        const client = await VitalClientService.getInstance();

        return await retryOnError(
            async () => {
                return await client.testkit.createOrder(orderCreatePayload);
            },
            [STATUS_CODE.requestTimeout, STATUS_CODE.conflict, STATUS_CODE.manyRequests, STATUS_CODE.internalServerError],
            { retries: RETRY_OPTION.retries, delay: RETRY_OPTION.delay }
        );
    } catch (error) {
        if (error instanceof VitalError) {
            throw new CustomError(
                `${ERROR_MESSAGES.VITAL.CREATE_ORDER}: "${orderCreatePayload.userId}" and lab test ID "${orderCreatePayload.labTestId}"`,
                ErrorSource.Testkit,
                ErrorPlatform.Vital,
                error,
            );
        } else {
            throw new CustomError(
                `${ERROR_MESSAGES.FAILURE}: ${orderCreatePayload}`,
                ErrorSource.Testkit,
                ErrorPlatform.Vital,
                error,
            );
        }
    }
}
