import type { AdminApiContextWithoutRest } from "node_modules/@shopify/shopify-app-remix/dist/ts/server/clients";
import type { LineItem, OrderResponse } from "~/models/shopify/webhook/order";
import { getCustomerWithMetafields } from "~/api/shopify/graphQL/customer";
import { getProduct } from "~/api/shopify/graphQL/product";
import { graphQLUtils } from "~/api/shopify/graphQL/utility";
import { OWNER_TYPE } from "~/utils/shopifyConstants";
import { CustomError } from "~/models/error";
import { ErrorPlatform } from "~/models/enums";
import { processRefund } from "./processRefundServices";
import { handleErrorLogging } from "../loggingService";
import { syncUserAndUpdateMetafield } from "./webhookService";
import db from "~/db.server";

export async function processLineItemWithRetry(
  admin: AdminApiContextWithoutRest,
  item: LineItem,
  orderId: string,
  dbLineItem: { id: bigint }
) {
  try {
    // Update line item status to processing
    await db.orderLineItem.update({
      where: { id: dbLineItem.id },
      data: {
        status: "processing",
        attempts: { increment: 1 },
        updatedAt: new Date(),
      },
    });

    // Get customer details from the order payload
    const shopifyCustomer = await getCustomerWithMetafields(
      admin,
      item.customerId || '' // Use empty string as fallback
    );

    if (!shopifyCustomer) {
      throw new Error(
        `Shopify customer is not found with the id ${item.customerId}`
      );
    }

    // Sync user with Vital if needed
    const user = await syncUserAndUpdateMetafield(admin, shopifyCustomer);

    // Get product details
    const product = await getProduct(
      admin,
      graphQLUtils.createGraphQLId(OWNER_TYPE.PRODUCT, item.product_id),
      "vital",
      "lab_test_id"
    );

    // Process based on product type
    switch (product.productType) {
      case PRODUCT_TYPE.HEALTH_PRODUCTS:
        const labTestId: string = product.metafield.value;
        if (user?.userId) {
          // Process each quantity as a separate order
          for (let i = 0; i < item.quantity; i++) {
            try {
              await processHealthProducts(
                admin,
                labTestId,
                user.userId,
                item
              );

              // Update processed count
              await db.orderLineItem.update({
                where: { id: dbLineItem.id },
                data: {
                  processedCount: { increment: 1 },
                  updatedAt: new Date(),
                },
              });

            } catch (error) {
              // Update failed count and error details
              await db.orderLineItem.update({
                where: { id: dbLineItem.id },
                data: {
                  failedCount: { increment: 1 },
                  lastError: error instanceof Error ? error.message : "Unknown error",
                  status: "failed",
                  retryAfter: new Date(Date.now() + 5000), // Retry after 5 seconds
                  updatedAt: new Date(),
                },
              });

              // If it's a Vital API error, we'll need to process a refund
              if (error instanceof CustomError && 
                  error.source === ErrorPlatform.Vital) {
                const orderResponse: OrderResponse = {
                  id: parseInt(orderId),
                  name: `Order ${orderId}`,
                  currency: "USD",
                  financial_status: "paid",
                  fulfillment_status: "unfulfilled",
                  confirmation_number: orderId,
                  confirmed: true,
                  created_at: new Date().toISOString(),
                  line_items: [item],
                  customer: {
                    id: parseInt(shopifyCustomer.id),
                    first_name: "",
                    last_name: "",
                    email: shopifyCustomer.email,
                    created_at: new Date().toISOString(),
                    updated_at: new Date().toISOString(),
                    admin_graphql_api_id: shopifyCustomer.id
                  },
                  shipping_address: {
                    name: "",
                    first_name: "",
                    last_name: "",
                    address1: "",
                    phone: "",
                    city: "",
                    country: "",
                    country_code: "",
                    zip: "",
                    province: "",
                    province_code: ""
                  },
                  billing_address: {
                    name: "",
                    first_name: "",
                    last_name: "",
                    address1: "",
                    phone: "",
                    city: "",
                    country: "",
                    country_code: "",
                    zip: "",
                    province: "",
                    province_code: ""
                  }
                };
                await processRefund(admin, orderResponse);
              }

              await handleErrorLogging({ admin, error, payload: item });
            }
          }
        }
        break;

      case PRODUCT_TYPE.HEALTH_PASS:
        // Process health pass product
        try {
          await processPayPerScanProduct(admin, item, shopifyCustomer);
          
          // Update processed count
          await db.orderLineItem.update({
            where: { id: dbLineItem.id },
            data: {
              processedCount: item.quantity,
              status: "completed",
              updatedAt: new Date(),
            },
          });
        } catch (error) {
          // Update error details
          await db.orderLineItem.update({
            where: { id: dbLineItem.id },
            data: {
              failedCount: item.quantity,
              lastError: error instanceof Error ? error.message : "Unknown error",
              status: "failed",
              retryAfter: new Date(Date.now() + 5000), // Retry after 5 seconds
              updatedAt: new Date(),
            },
          });
          await handleErrorLogging({ admin, error, payload: item });
        }
        break;
    }

    // If all quantities were processed successfully
    if (item.quantity === await getProcessedCount(dbLineItem.id)) {
      await db.orderLineItem.update({
        where: { id: dbLineItem.id },
        data: {
          status: "completed",
          updatedAt: new Date(),
        },
      });
    }

  } catch (error) {
    // Log any unexpected errors
    console.error("Error processing line item:", error);
    
    // Update line item status
    await db.orderLineItem.update({
      where: { id: dbLineItem.id },
      data: {
        status: "failed",
        lastError: error instanceof Error ? error.message : "Unknown error",
        updatedAt: new Date(),
      },
    });
    
    throw error;
  }
}

async function getProcessedCount(lineItemId: bigint): Promise<number> {
  const lineItem = await db.orderLineItem.findUnique({
    where: { id: lineItemId },
    select: { processedCount: true },
  });
  return lineItem?.processedCount || 0;
}

export async function processQueuedOrder(
  admin: AdminApiContextWithoutRest,
  lineItems: LineItem[],
  orderId: string
) {
  try {
    // Create line item records if they don't exist
    for (const item of lineItems) {
      const product = await getProduct(
        admin,
        graphQLUtils.createGraphQLId(OWNER_TYPE.PRODUCT, item.product_id),
        "vital",
        "lab_test_id"
      );

      const dbLineItem = await db.orderLineItem.upsert({
        where: {
          orderId_lineItemId: {
            orderId: BigInt(orderId),
            lineItemId: item.id.toString(),
          },
        },
        create: {
          orderId: BigInt(orderId),
          lineItemId: item.id.toString(),
          productId: item.product_id.toString(),
          productTitle: item.title,
          vendor: item.vendor,
          productType: product.productType,
          quantity: item.quantity,
          status: "pending",
        },
        update: {},
      });

      // Process the line item
      await processLineItemWithRetry(admin, item, orderId, dbLineItem);
    }

    // Check if all line items are completed
    const allLineItems = await db.orderLineItem.findMany({
      where: { orderId: BigInt(orderId) },
    });

    const allCompleted = allLineItems.every(
      (item) => item.status === "completed" || item.attempts >= item.maxAttempts
    );

    // Update the webhook task status
    await db.ordersWebhookTask.update({
      where: { id: BigInt(orderId) },
      data: {
        status: allCompleted ? "completed" : "failed",
        updated_at: new Date(),
      },
    });

  } catch (error) {
    // Update the webhook task with error status
    await db.ordersWebhookTask.update({
      where: { id: BigInt(orderId) },
      data: {
        status: "failed",
        last_error: error instanceof Error ? error.message : "Unknown error",
        updated_at: new Date(),
      },
    });
    throw error;
  }
}