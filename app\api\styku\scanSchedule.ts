import type APIPayload from "~/models/apiPayload";
import type APIResponse from "~/models/apiResponse";
import type { ScanScheduleRequest, ScanScheduleResponse } from "~/models/styku/scanSchedule";
import { API_PAYLOADS } from "~/utils/apiConstants";
import { makeRequest } from "../fetch";

/**
 * Schedule a scan.
 * @param payload The request payload for scheduling a scan.
 * @returns A promise that resolves to the response containing scheduled scan data.
 */
export default async function scheduleScan(payload: ScanScheduleRequest): Promise<APIResponse<ScanScheduleResponse>> {
  const apiPayload: APIPayload<ScanScheduleRequest> = API_PAYLOADS.scheduleScan;
  apiPayload.body = payload;
  return await makeRequest(apiPayload);
}
