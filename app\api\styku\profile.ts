import type APIPayload from "~/models/apiPayload";
import type APIResponse from "~/models/apiResponse";
import type { Password, Profile } from "~/models/styku/profile";
import type Save from "~/models/save";
import { API_PAYLOADS } from "~/utils/apiConstants";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "~/utils/stringConstants";
import { makeRequest } from "../fetch";

/**
 * Get profile data
 * @param authToken Authentication token
 * @returns Profile data
 */
export async function getProfile(authToken: string | null): Promise<APIResponse<Profile>> {
  const apiPayload: APIPayload<null> = API_PAYLOADS.getProfile;
  apiPayload.addAuthHeader(authToken);
  apiPayload.successMessage = SUCCESS_MESSAGES.STYKU.GET_PROFILE_SUCCESS;
  apiPayload.failureMessage = ERROR_MESSAGES.STYKU.GET_PROFILE;

  return await makeRequest<null, Profile>(apiPayload);
}

/**
 * Change profile password
 * @param authToken Authentication token
 * @returns Profile data
 */
export async function changePassword(payload: Password, authToken: string | null): Promise<APIResponse<Save>> {
  const apiPayload: APIPayload<Password> = API_PAYLOADS.changePassword;
  apiPayload.addAuthHeader(authToken);
  apiPayload.body = payload;
  apiPayload.successMessage = SUCCESS_MESSAGES.CHANGE_PASSWORD_SUCCESS;
  apiPayload.failureMessage = ERROR_MESSAGES.STYKU.CHANGE_PASSWORD;
  return await makeRequest(apiPayload);
}
