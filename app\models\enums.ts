export enum ErrorSource {
    Customer = 'customer',
    Order = 'order',
    User = 'user',
    Product = 'product',
    Metafield = 'metafield',
    Metaobject = 'metaobject',
    MetafieldDefinition = 'metafield_definition',
    MetaobjectDefinition = 'metaobject_definition',
    Testkit = 'testkit',
    LabTest = 'lab_test',
    Result = 'result',
    Webhook = 'webhook'
}

export enum ErrorPlatform {
    Shopify = 'Shopify',
    Vital = 'Vital',
}

export enum ErrorName {
    ShopifyError = "Shopify Error",
    VitalError = "Vital Error",
}