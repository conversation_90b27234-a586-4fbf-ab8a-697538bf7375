import type { EmptyStateProps } from "@shopify/polaris";
import { Card, EmptyState } from "@shopify/polaris";

interface EmptyStatePlaceholderProps {
  heading: string;
  message: string;
  action?: EmptyStateProps["action"];
}

export default function EmptyStatePlaceholder({
  heading,
  message,
  action,
}: EmptyStatePlaceholderProps) {
  return (
    <Card>
      <EmptyState
        heading={heading}
        action={action}
        image="https://cdn.shopify.com/s/files/1/0262/4071/2726/files/emptystate-files.png"
      >
        <p>{message}</p>
      </EmptyState>
    </Card>
  );
}
