import { json, type ActionFunctionArgs } from "@remix-run/node";
import {
  login,
  resetPassword,
  secureAccount,
  signOut,
} from "~/api/styku/account";
import {
  searchShopifyCustomerByEmail,
  updateShopifyCustomerDetailsAsync,
} from "~/api/shopify/rest/customer";
import type APIResponse from "~/models/apiResponse";
import type { LoginRequestPayload, LoginResponse } from "~/models/styku/login";
import type { EmailPassword, ProfilePassword } from "~/models/styku/profile";
import type Save from "~/models/save";
import { authenticate } from "~/shopify.server";
import { HEADERS } from "~/utils/apiConstants";
import { SUCCESS_MESSAGES } from "~/utils/stringConstants";

export async function action({ request, params }: ActionFunctionArgs) {
  const { session, admin } = await authenticate.public.appProxy(request);
  const subpath: string | undefined = params.subpath;

  switch (subpath) {
    case "secure":
      const accountPayload: ProfilePassword =
        (await request.json()) as ProfilePassword;

      const secureAccountResponse = await secureAccountAsync(accountPayload);

      return json(secureAccountResponse);
    case "login":
      const loginPayload: LoginRequestPayload =
        (await request.json()) as LoginRequestPayload;

      const stykuLoginResponse = await loginAsync(loginPayload);

      return json(stykuLoginResponse);
    case "reset-password":
      const resetPasswordPayload: EmailPassword =
        (await request.json()) as EmailPassword;

      const stykuResetPasswordResponse: any =
        await resetPasswordAsync(resetPasswordPayload);

      if (stykuResetPasswordResponse.status === 200 && admin) {
        const password = resetPasswordPayload.password;

        const shopifyCustomer = await searchShopifyCustomerByEmail(
          admin,
          session,
          resetPasswordPayload,
        );

        await updateShopifyCustomerDetailsAsync(
          admin,
          session,
          shopifyCustomer.id,
          { password },
        );

        return json(
          { message: SUCCESS_MESSAGES.SUCCESS, status: 200 },
          { status: 200 },
        );
      }

      return json(stykuResetPasswordResponse);
    case "sign-out":
      return await signOutAsync(request);
    default:
      return json({ status: 404, message: "API not found" }, { status: 404 });
  }
}

/**
 * Secure a profile account
 * Returns:
 * 1. 200 - On success
 * 2. 601 - Invalid email address
 * 3. 605 - Profile already secured
 * 4. 606 - Invalid password or mismatch with confirm password
 * 5. 607 - If password is weak
 * 6. 608 - If profile email is not verified
 * 7. 617 - Secure process is failed
 * 8. 500 - Other errors
 */
export async function secureAccountAsync(payload: ProfilePassword) {
  const response: APIResponse<boolean> = await secureAccount(payload);
  return response;
}

/**
 * Login profile account
 * Returns:
 * 1. 200 - On success
 * 2. 630 - Invalid username or password
 * 3. 500 - Other errors
 */
export async function loginAsync(loginPayload: LoginRequestPayload) {
  const response: APIResponse<LoginResponse> = await login(loginPayload);
  return response;
}

/**
 * Reset profile password
 * Returns:
 * 1. 200 - Returns the true if the password is reset successfully
 * 2. 602 - If profile is not found
 * 3. 607 - If password strength is weak
 * 4. 615 - If password does not match with confirm password
 * 6. 617 - If profile is unauthorized (Email not verified before)
 * 7. 500 - Other errors
 */
export async function resetPasswordAsync(resetPasswordPayload: EmailPassword) {
  const response: APIResponse<Save> = await resetPassword(resetPasswordPayload);
  return response;
}

/**
 * Sign out profile
 * Returns:
 * 1. 200 - Returns the true if the password is reset successfully
 * 2. 401 - Invalid auth token
 * 3. 602 - If profile is not found
 * 4. 500 - Other errors
 */
export async function signOutAsync(request: Request) {
  const authToken: string | null = request.headers.get(
    HEADERS.X_STYKU_AUTHORIZATION,
  );
  const response: APIResponse<boolean> = await signOut(authToken);
  return response;
}
