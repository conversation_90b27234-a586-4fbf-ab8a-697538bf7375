import type { ShopifyMetaobjectDefinition } from "~/models/shopify/metaobject";

export const SHOPIFY_QUERY_PARAMS_KEY = {
  loggedInCustomerId: "logged_in_customer_id",
};

export const REFUND = {
  kind: {
    refund: "REFUND",
  },
  staffNote: "This is canceled automatically: Failed in the vital",
};

export const OWNER_TYPE = {
  CUSTOMER: "Customer",
  PRODUCT: "Product",
  ORDER: "Order",
  LINEITEM: "LineItem",
};

export const LIMIT = {
  DEFAULT: 20,
  MAX: 250,
};

export const PRODUCT_TITLE = {
  HEALTH_PASS: "Health Pass",
};

export const PRODUCT_TYPE = {
  HEALTH_PRODUCTS: "health-products",
  HEALTH_PASS: "health-pass",
  SUBSCRIPTION: "subscription",
};

export const PRODUCT_VENDOR = {
  VITAL: "Vital",
  HEALTH_PASS: "Health Pass"
};

export const STATUS = {
  ACTIVE: "ACTIVE",
  SUCCESS: "SUCCESS",
};

export const METAFIELD = {
  KEY: {
    AVAILABLE_SCAN_BUNDLES: "available_scan_bundles",
    LAB_TEST_ID: "lab_test_id",
    USER_ID: "user_id",
    ORDERS: "orders",
    VITAL_ORDERS: "vital_orders",
    ERROR_LOGS: "error_logs",
    PROFILE_ID: "profile_id",
    FREE_PRODUCTS_WITH_SUBSCRIPTION: "free_products_with_subscription"
  },
  NAMESPACE: {
    STYKU: "styku",
    VITAL: "vital",
    SUBSCRIPTION: "subscription",
    ERROR: "error",

  },
  TYPE: {
    NUMBER_INTEGER: "number_integer",
    SINGLE_LINE_TEXT_FIELD: "single_line_text_field",
    LIST_METAOBJECT_REFERENCE: "list.metaobject_reference",
  },
  OWNER_TYPE: {
    ORDER: "ORDER",
    CUSTOMER: "CUSTOMER",
  },
};

export const BUSINESS_TYPE_META_DEFINITION: ShopifyMetaobjectDefinition = {
  title: "Business Types",
  type: "business_type",
  fieldDefinitions: [
    {
      key: "business_id",
      name: "Business id",
      type: "number_integer",
    },
    {
      key: "business_name",
      name: "Business name",
      type: "single_line_text_field",
    },
    {
      key: "business_name_constant",
      name: "Business name constant",
      type: "single_line_text_field",
    },
  ],
};

export const STYKU_LOCATION_META_DEFINITION: ShopifyMetaobjectDefinition = {
  title: "Styku Locations",
  type: "styku_location",
  fieldDefinitions: [
    {
      key: "user_id",
      name: "User id",
      type: "single_line_text_field",
    },
    {
      key: "is_health_pass_partner",
      name: "Is health pass partner",
      type: "boolean",
    },
    {
      key: "company_name",
      name: "Company name",
      type: "single_line_text_field",
    },
    {
      key: "city",
      name: "City",
      type: "single_line_text_field",
    },
    {
      key: "state",
      name: "State",
      type: "single_line_text_field",
    },
    {
      key: "country",
      name: "Country",
      type: "single_line_text_field",
    },
    {
      key: "address",
      name: "Address",
      type: "single_line_text_field",
    },
    {
      key: "postal_code",
      name: "Postal code",
      type: "single_line_text_field",
    },
    {
      key: "latitude",
      name: "Latitude",
      type: "number_decimal",
    },
    {
      key: "longitude",
      name: "Longitude",
      type: "number_decimal",
    },
    {
      key: "phone",
      name: "Phone",
      type: "single_line_text_field",
    },
    {
      key: "email",
      name: "Email",
      type: "single_line_text_field",
    },
    {
      key: "website",
      name: "Website",
      type: "single_line_text_field",
    },
    {
      key: "business_type_name",
      name: "Business type name",
      type: "single_line_text_field",
    },
  ],
};

export const SCHEDULED_SCAN_META_DEFINITION: ShopifyMetaobjectDefinition = {
  title: "Scheduled Scans",
  type: "scheduled_scan",
  fieldDefinitions: [
    {
      key: "first_name",
      name: "First name",
      type: "single_line_text_field",
    },
    {
      key: "last_name",
      name: "Last name",
      type: "single_line_text_field",
    },
    {
      key: "email",
      name: "Email",
      type: "single_line_text_field",
    },
    {
      key: "phone_number",
      name: "Phone number",
      type: "single_line_text_field",
    },
    {
      key: "user_id",
      name: "User id",
      type: "number_integer",
    },
    {
      key: "profile_id",
      name: "Profile id",
      type: "number_integer",
    },
  ],
};

export const VITAL_ORDERS_META_DEFINITION: ShopifyMetaobjectDefinition = {
  title: "Vital orders",
  type: "vital_orders",
  fieldDefinitions: [
    {
      key: "order_id",
      name: "Order id",
      type: "single_line_text_field",
    },
    {
      key: "user_id",
      name: "User id",
      type: "single_line_text_field",
    },
    {
      key: "shopify_order_id",
      name: "Shopify order id",
      type: "single_line_text_field",
    },
    {
      key: "product_name",
      name: "Product name",
      type: "single_line_text_field",
    },
    {
      key: "created_at",
      name: "Created At",
      type: "date_time",
    },
    {
      key: "updated_at",
      name: "Updated At",
      type: "date_time",
    },
    {
      key: "sample_id",
      name: "Sample id",
      type: "single_line_text_field",
    },
    {
      key: "report",
      name: "Report",
      type: "url",
    },
    {
      key: "events",
      name: "Events",
      type: "json",
    },
    {
      key: "status",
      name: "Status",
      type: "single_line_text_field",
    },
    {
      key: "outbound_tracking_number",
      name: "Outbound tracking number",
      type: "single_line_text_field",
    },
    {
      key: "outbound_tracking_url",
      name: "Outbound tracking URL",
      type: "single_line_text_field",
    },
    {
      key: "outbound_courier",
      name: "Outbound courier",
      type: "single_line_text_field",
    },
  ],
};

export const ERROR_META_DEFINITION: ShopifyMetaobjectDefinition = {
  title: "Error Logs",
  type: "error_logs",
  fieldDefinitions: [
    {
      key: "context",
      name: "Context (Where the error occurred)",
      type: "single_line_text_field",
    },
    {
      key: "error",
      name: "Error",
      type: "json",
    },
    {
      key: "is_critical",
      name: "Critical",
      type: "boolean",
    },
    {
      key: "created_at",
      name: "Created At",
      type: "date_time",
    },
    {
      key: "updated_at",
      name: "Updated At",
      type: "date_time",
    },
  ],
};
