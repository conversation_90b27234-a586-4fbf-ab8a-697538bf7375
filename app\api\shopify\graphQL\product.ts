import type  { AdminApiContextWithoutRest } from "node_modules/@shopify/shopify-app-remix/dist/ts/server/clients";
import type { ProductInput } from "~/models/shopify/productInput";
import { LIMIT, METAFIELD } from "~/utils/shopifyConstants";
import type { ProductVariantInput } from "~/models/shopify/ProductVariant";
import type { Product, ProductsResponse } from "~/models/shopify/productResponse";
import { graphQLUtils } from "./utility";
import { CustomError } from "~/models/error";
import { ERROR_MESSAGES } from "~/utils/stringConstants";
import { ErrorSource, ErrorPlatform } from "~/models/enums";

/**
 * Creates a new product in Shopify based on the provided data.
 * @param admin - The Shopify admin API context used to make GraphQL requests.
 * @param product - The lab test data to create the product from.
 * @returns A promise that resolves to the created product and its variant.
 */
export async function createProduct(
  admin: AdminApiContextWithoutRest,
  product: ProductInput,
) {
  const { title, handle, productType, vendor, status, metafields } = product;

  const response = await admin.graphql(
    `#graphql
        mutation populateProduct($input: ProductInput!) {
          productCreate(input: $input) {
            product {
              id
              title
              handle
              status
              variants(first: 1) {
                edges {
                  node {
                    id
                    price
                    createdAt
                  }
                }
              }
            }
          }
        }`,
    {
      variables: {
        input: {
          title: title,
          handle: handle,
          productType: productType,
          vendor: vendor,
          status: status,
          metafields: [
            {
              namespace: metafields.namespace,
              key: metafields.key,
              value: metafields.value,
              type: metafields.type,
            },
          ],
        },
      },
    },
  );

  const responseJson = await response.json();

  return {
    product: responseJson.data.productCreate.product,
  };
}

/**
 * Updates the price of a product variant in Shopify.
 * @param admin - The Shopify admin API context used to make GraphQL requests.
 * @param variantId - The ID of the variant to update.
 * @param price - The new price for the variant.
 * @returns A promise that resolves to the updated variant.
 */
export async function updateProductVariant(
  admin: AdminApiContextWithoutRest,
  productVariantInput: ProductVariantInput,
) {
  const variantResponse = await admin.graphql(
    `#graphql
        mutation updateVariant($input: ProductVariantInput!) {
          productVariantUpdate(input: $input) {
            productVariant {
              id
              price
              createdAt
            }
          }
        }`,
    {
      variables: {
        input: {
          id: productVariantInput.id,
          price: productVariantInput.price,
        },
      },
    },
  );

  const variantResponseJson = await variantResponse.json();

  return variantResponseJson.data.productVariantUpdate.productVariant;
}

/**
 * Fetches products from Shopify based on various criteria.
 * @param admin - The admin API context.
 * @param productType - The type of product to query.
 * @param vendor - The vendor of the product.
 * @param limit - The maximum number of products to fetch.
 * @param metafieldNamespace - The namespace of the metafield to query.
 * @param metafieldKey - The key of the metafield to query.
 * @param cursor - The cursor for pagination. (Usage:  )
 * @returns A promise resolving to the fetched products along with pagination info.
 */
export async function getProducts(
  admin: AdminApiContextWithoutRest,
  limit: number = LIMIT.DEFAULT,
  productType?: string,
  vendor?: string,
  namespace?: string,
  key?: string,
  cursor?: string,
): Promise<ProductsResponse> {
  try {
    // Build the query string with optional filters
    let queryConditions = `first: ${limit}`;

    // Use query parameter to filter products
    let filterQuery = "";

    if (productType) {
      filterQuery += `product_type:${productType} `;
    }

    if (vendor) {
      filterQuery += `vendor:${vendor} `;
    }

    if (cursor) {
      queryConditions += `, after: "${cursor}"`;
    }

    const metafieldsFragment = graphQLUtils.buildMetafieldFragment(namespace, key);

    // Construct the GraphQL query with optional cursor
    const query = `
        query {
          products(${queryConditions}, query: "${filterQuery.trim()}") {
            edges {
              node {
                id
                title
                handle
                vendor
                productType
                variants(first: 1) {
                  edges {
                    node {
                      id
                      price
                    }
                  }
                }
                 ${metafieldsFragment}
              }
            }
            pageInfo {
              hasNextPage
              endCursor
            }
          }
        }
      `;

    const response = await admin.graphql(query);
    const { data } = await response.json();

    return data.products;
  } catch (error) {
    console.error("Error while fetching products: ", error);
    throw error;
  }
}

export async function getProduct(
  admin: AdminApiContextWithoutRest,
  productId: string,
  namespace?: string,
  key?: string
): Promise<Product> {
  try {

    const response = await admin.graphql(
      `#graphql
        query product($id: ID!) {
          product(id: $id) {
            title
            vendor
            id
            title
            handle
            vendor
            productType
            variants(first: 1) {
              edges {
                node {
                  id
                  price
                }
              }
            }
            labTestIdMetafield: metafield (namespace: "${METAFIELD.NAMESPACE.VITAL}", key: "${METAFIELD.KEY.LAB_TEST_ID}") {
              key,
              value
            }
            assignedFreeProductsMetafiled: metafield (namespace: "${METAFIELD.NAMESPACE.SUBSCRIPTION}", key: "${METAFIELD.KEY.FREE_PRODUCTS_WITH_SUBSCRIPTION}") {
              key,
              value
            }
          }
        }`,
      {
        variables: {
          id: productId,
        },
      }
    );

    const { data } = await response.json();

    return data.product;
  } catch (error) {
    throw new CustomError(
      `${ERROR_MESSAGES.SHOPIFY.GET_PRODUCT} product id: "${productId}".`,
      ErrorSource.Product,
      ErrorPlatform.Shopify,
      error,
    );
  }
}
