import type { ClientFacingOrder } from "@tryvital/vital-node/api";

export type ShopifyMetaobjectFieldDefinition = {
  key: string;
  name: string;
  type: string;
};

export type ShopifyMetaobjectFieldInput = {
  key: string;
  value: string | undefined;
};

export type ShopifyMetaobjectUpsertInput = {
  fields: ShopifyMetaobjectFieldInput[];
};

export type ShopifyMetaobjectDefinition = {
  title: string;
  type: string;
  fieldDefinitions: ShopifyMetaobjectFieldDefinition[]; // object should be type of MetaobjectFieldDefinition
};

export interface MetaobjectRefParams {
  definition: ShopifyMetaobjectDefinition;
  namespace: string;
  key: string;
  type: string;
  ownerType: string;
}

export interface MetaobjectCreateResponse {
  metaobject: {
    id: string;
  };
  userErrors?: Array<UserError>;
}

export interface UserError {
  field: string[];
  message: string;
}

export class VitalOrdersMetaobject {
  fields: ShopifyMetaobjectFieldInput[];

  constructor(fields: ShopifyMetaobjectFieldInput[]) {
    this.fields = fields;
  }

  // Static method to create an instance from an order object
  static create(order: ClientFacingOrder, shopifyOrderId: number): ShopifyMetaobjectUpsertInput {
    const fields: ShopifyMetaobjectFieldInput[] = [
      {
        key: "order_id",
        value: order.id,
      },
      {
        key: "product_name",
        value: order.labTest.name,
      },
      {
        key: "created_at",
        value: new Date(order.createdAt).toISOString(),
      },
      {
        key: "updated_at",
        value: new Date(order.updatedAt).toISOString(),
      },
      {
        key: "status",
        value: order.status,
      },
      {
        key: "events",
        value: JSON.stringify(
          order.events.map((event) => ({
            id: event.id,
            created_at: new Date(event.createdAt).toISOString(),
            status: event.status,
          })),
        ),
      },
      {
        key: "user_id",
        value: order.userId,
      },
      {
        key: "shopify_order_id",
        value: shopifyOrderId.toString(),
      },
    ];
    return new VitalOrdersMetaobject(fields);
  }
}
