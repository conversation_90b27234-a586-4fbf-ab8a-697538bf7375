import type { AdminApiContextWithoutRest } from "node_modules/@shopify/shopify-app-remix/dist/ts/server/clients";
import { ErrorPlatform, ErrorSource } from "~/models/enums";
import { CustomError } from "~/models/error";
import type { OrderDetailsResponse, RefundCreateResponse, RefundInput } from "~/models/shopify/order";
import { LIMIT } from "~/utils/shopifyConstants";
import { ERROR_MESSAGES } from "~/utils/stringConstants";

/**
 * Fetches detailed information for a Shopify order.
 *
 * @param admin - Shopify admin API context.
 * @param orderId - The ID of the order to fetch.
 * @returns The details of the order including line items, transactions, and fulfillment orders.
 * @throws If the query fails or an error occurs.
 */
export async function getOrderDetails(
  admin: AdminApiContextWithoutRest,
  orderId: string,
  limit: number = LIMIT.MAX,
): Promise<OrderDetailsResponse> {
  try {
    const response = await admin.graphql(
      `#graphql
        query orderDetails($orderId: ID!, $limit:  Int!) {
          order(id: $orderId) {
            id
            lineItems(first: $limit) {
              nodes {
                id
                title
                name
                quantity
                originalUnitPriceSet {
                  presentmentMoney {
                    amount
                    currencyCode
                  }
                }
                product {
                  productType
                  vendor
                }
              }
            }
            transactions {
              status
              gateway
              paymentId
              id
            }
            fulfillmentOrders(first: $limit) {
              nodes {
                id
                orderId
                orderName
                status
                lineItems(first: $limit) {
                  nodes {
                    id
                    productTitle
                    totalQuantity
                    vendor
                  }
                }
              }
            }
          }
        }`,
      {
        variables: {
          orderId,
          limit
        },
      },
    );

    const { data } = await response.json();

    return data.order;
  } catch (error) {
    throw new CustomError(
      `${ERROR_MESSAGES.SHOPIFY.GET_ORDER} details for order ID: "${orderId}".`,
      ErrorSource.Order,
      ErrorPlatform.Shopify,
      error,
    );
  }
}

/**
 * Creates a refund for a Shopify order.
 *
 * @param admin - Shopify admin API context.
 * @param input - RefundInput containing details for the refund.
 * @returns Refund creation response including refunded line items, errors, and refund details.
 * @throws If the refund creation fails or an error occurs.
 */
export async function createRefund(
  admin: AdminApiContextWithoutRest,
  input: RefundInput,
): Promise<RefundCreateResponse> {
  try {
    const response = await admin.graphql(
      `#graphql
        mutation refundCreate($input: RefundInput!) {
          refundCreate(input: $input) {
            userErrors {
              field
              message
            }
            refund {
              id
              note
              totalRefundedSet {
                presentmentMoney {
                  amount
                }
              }
            }
          }
        }`,
      {
        variables: { input },
      },
    );

    const { data } = await response.json();

    if (data.refundCreate.userErrors?.length > 0) {
      throw new CustomError(
        `${ERROR_MESSAGES.SHOPIFY.CREATE_REFUND} for order ID: "${input.orderId}".`,
        ErrorSource.Order,
        ErrorPlatform.Shopify,
        data.refundCreate.userErrors,
      );
    }

    return data.refundCreate.refund;
  } catch (error) {
    if (error instanceof CustomError) {
      throw error;
    }

    throw new CustomError(
      `${ERROR_MESSAGES.SHOPIFY.CREATE_REFUND} for order ID: "${input.orderId}".`,
      ErrorSource.Order,
      ErrorPlatform.Shopify,
      error,
    );
  }
}
