export interface Email {
  email?: string;
}

export interface EmailCode extends Email {
  code?: string;
}

export interface BaseProfile extends Email {
  firstName?: string;
  lastName?: string;
}

export interface EmailPassword extends Email {
  password?: string;
  confirmPassword?: string;
}

export interface Password {
  oldPassword: string,
  newPassword: string;
  confirmPassword: string;
}

export interface ProfilePassword extends BaseProfile, EmailPassword { }

export interface Profile extends BaseProfile {
  scansCount?: number;
  phoneNumber?: string;
  birthday?: string;
  isMale?: boolean;
  unit?: string;
  heightInInches?: number;
  lastScanDate?: Date;
}
