import { json, type ActionFunctionArgs } from "@remix-run/node";
import { verifyProfile } from "~/api/styku/account";
import type APIResponse from "~/models/apiResponse";
import { authenticate } from "~/shopify.server";

/**
 * Verify the profile email
 * Returns:
 * 1. 200 - Successfully sent a verification code
 * 2. 601 - Invalid email address
 * 3. 603 - Unable to send an email code
 * 3. 605 - Profile is already secured with password
 * 4. 500 - Other errors
 */
export async function action({ request }: ActionFunctionArgs) {
  await authenticate.public.appProxy(request);

  const { email } = await request.json();
  const response: APIResponse<boolean> = await verifyProfile(email);
  return json(response);
}
