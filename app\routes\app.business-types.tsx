import type { LoaderFunctionArgs } from "@remix-run/node";
import { getBusinessTypes } from "~/api/styku/businessTypes";
import {
  checkAndCreateMetaobjectDefinition,
  upsertMetaobject,
} from "~/api/shopify/graphQL/metaObject";
import BusinessType from "~/models/styku/businessType";
import { authenticate } from "~/shopify.server";
import { BUSINESS_TYPE_META_DEFINITION } from "~/utils/shopifyConstants";
import { SUCCESS_MESSAGES } from "~/utils/stringConstants";

export async function loader({ request }: LoaderFunctionArgs) {
  try {
    const { admin } = await authenticate.admin(request);

    // Check for the business type definition in Shopify and create if it is not present
    await checkAndCreateMetaobjectDefinition(
      admin,
      BUSINESS_TYPE_META_DEFINITION,
    );

    // Fetch business types from the Styku service
    const businessTypes: BusinessType[] = await getBusinessTypes();

    // Push all objects to Shopify
    for (const businessType of businessTypes) {
      await upsertMetaobject(
        admin,
        BUSINESS_TYPE_META_DEFINITION.type,
        businessType.id.toString(),
        BusinessType.toShopifyMetaobject(businessType),
      );
    }

    return SUCCESS_MESSAGES.STYKU.BUSINESS_TYPES_CREATED;
  } catch (error) {
    console.error(error);
    throw error;
  }
}
