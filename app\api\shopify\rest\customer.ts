import type { AdminApiContextWithoutRest } from "node_modules/@shopify/shopify-app-remix/dist/ts/server/clients";
import type { SessionData } from "@remix-run/node";
import type {
  Email,
  EmailPassword,
  ProfilePassword,
} from "~/models/styku/profile";

// Function to update the password of a customer in Shopify
export async function updateShopifyCustomerDetailsAsync(
  admin: AdminApiContextWithoutRest,
  session: SessionData | undefined,
  loggedInCustomerId: string | null,
  { password, firstName, lastName }: ProfilePassword,
) {
  try {
    // Create a customer update object
    const customerToUpdate = new admin.rest.resources.Customer({
      session: session,
    });
    // Set customer properties to update
    customerToUpdate.id = loggedInCustomerId;

    if (password) {
      customerToUpdate.password = password;
      customerToUpdate.password_confirmation = password;
    }

    firstName && (customerToUpdate.first_name = firstName);

    lastName && (customerToUpdate.last_name = lastName);

    // Save the customer update
    await customerToUpdate.save({
      update: true,
    });

    return customerToUpdate;
  } catch (error) {
    console.error(
      `Error while changing the password for the customer ${loggedInCustomerId} into Shopify`,
      { error },
    );
  }
}

// Function to search for a customer by email in Shopify
export async function searchShopifyCustomerByEmail(
  admin: AdminApiContextWithoutRest,
  session: SessionData | undefined,
  payload: EmailPassword,
) {
  try {
    const email = payload.email;
    const searchResult = await admin.rest.resources.Customer.search({
      session: session,
      fields: "id, first_name, last_name, email,",
      query: `email:${email}`,
    });

    const customer = searchResult.customers;

    // Filter the search results to find the exact customer email match
    return customer.find((customer: Email) => customer.email === email);
  } catch (error) {
    console.error(`Error while searching customer into Shopify`, { error });
    throw error;
  }
}

// Function to find a customer by customer ID in Shopify
export async function findShopifyCustomerById(
  admin: AdminApiContextWithoutRest,
  session: SessionData | undefined,
  customerId: string | null,
) {
  try {
    const customer = await admin.rest.resources.Customer.find({
      session: session,
      fields: "id, first_name, last_name, email,",
      id: customerId,
    });

    return customer;
  } catch (error) {
    console.error(`Error while Finding customer into Shopify`, { error });
    throw error;
  }
}
