import type { ClientFacingLabTest } from "@tryvital/vital-node/api";
import VitalClientService from "./vitalClient";
import { CustomError } from "~/models/error";
import { ERROR_MESSAGES } from "~/utils/stringConstants";
import { VitalError } from "@tryvital/vital-node";
import { ErrorSource, ErrorPlatform } from "~/models/enums";


export async function getLabTests(): Promise<ClientFacingLabTest[]> {
    try {
        const client = await VitalClientService.getInstance();

        return await client.labTests.get();
    } catch (error) {
        if (error instanceof VitalError) {
            throw new CustomError(
                `${ERROR_MESSAGES.VITAL.GET_LAB_TESTS}`,
                ErrorSource.LabTest,
                ErrorPlatform.Vital,
                error,
            );
        } else {
            throw new CustomError(
                `${ERROR_MESSAGES.FAILURE}`,
                ErrorSource.LabTest,
                ErrorPlatform.Vital,
                error,
            );
        }

    }
}
