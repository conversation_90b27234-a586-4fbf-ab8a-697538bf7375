import type { AdminApiContextWithoutRest } from "node_modules/@shopify/shopify-app-remix/dist/ts/server/clients";
import type { ProductInput } from "~/models/shopify/productInput";
import type { ProductVariantInput } from "~/models/shopify/ProductVariant";
import type { CreateProductResponse } from "~/models/shopify/productResponse";

import {
  createProduct,
  updateProductVariant,
} from "~/api/shopify/graphQL/product";
/**
 * Creates a product and updates its variant price.
 * @param admin - The Shopify admin API context used to make GraphQL requests.
 * @param product - The lab test data to create the product from.
 * @returns A promise that resolves to an object containing the created product and updated variant.
 */
export async function createProductAndUpdateVariant(
  admin: AdminApiContextWithoutRest,
  product: ProductInput,
) {
  // Create the product
  const { product: createdProduct }: CreateProductResponse =
    await createProduct(admin, product);

  const variantId = createdProduct.variants.edges[0].node.id;

  const productVariantInput: ProductVariantInput = {
    id: variantId,
    price: product.price,
  };

  // Update the variant price
  const updatedVariant = await updateProductVariant(admin, productVariantInput);

  return {
    product: createdProduct,
    variant: updatedVariant,
  };
}
