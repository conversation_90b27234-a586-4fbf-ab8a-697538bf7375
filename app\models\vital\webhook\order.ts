interface LabTest {
    name: string;
}

export interface Order {
    id: string;
    status: string;
    user_id: string;
    sample_id: string;
    created_at: string;
    updated_at: string;
    lab_test: LabTest;
    details: {
        data: {
            id: string;
            created_at: string;
            updated_at: string;
            shipment: Shipment;
        };
    };
    events: OrderEvent[];
}

interface OrderEvent {
    created_at: string;
    id: number;
    status: string;
}

interface Shipment {
    id: string;
    outbound_courier: string | null;
    outbound_tracking_number: string | null;
    outbound_tracking_url: string | null;
}

