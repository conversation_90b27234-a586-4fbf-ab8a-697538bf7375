import { json } from "@remix-run/node";
import type { ClientActionFunctionArgs } from "@remix-run/react";
import { changePassword } from "~/api/styku/profile";
import { updateShopifyCustomerDetailsAsync } from "~/api/shopify/rest/customer";
import type APIResponse from "~/models/apiResponse";
import type { Password } from "~/models/styku/profile";
import type Save from "~/models/save";
import { authenticate } from "~/shopify.server";
import { HEADERS } from "~/utils/apiConstants";
import { SHOPIFY_QUERY_PARAMS_KEY } from "~/utils/shopifyConstants";
import { SUCCESS_MESSAGES } from "~/utils/stringConstants";

export async function action({ request, params }: ClientActionFunctionArgs) {
  const { session, admin } = await authenticate.public.appProxy(request);
  const subpath: string | undefined = params.subpath;
  const { searchParams } = new URL(request.url);
  const payload: Password = await request.json();
  const loggedInCustomerId: string | null = searchParams.get(
    SHOPIFY_QUERY_PARAMS_KEY.loggedInCustomerId,
  );

  switch (subpath) {
    case "change-password":
      const stykuResponse = await changeStykuPasswordAsync(request, payload);

      if (stykuResponse.status === 200 && admin) {
        const password = payload.newPassword;

        await updateShopifyCustomerDetailsAsync(
          admin,
          session,
          loggedInCustomerId,
          { password },
        );

        return json(
          { message: SUCCESS_MESSAGES.CHANGE_PASSWORD_SUCCESS, status: 200 },
          { status: 200 },
        );
      } else {
        return json(stykuResponse);
      }
    default:
      return json({ status: 404, message: "API not found" }, { status: 404 });
  }
}

/**
 * Change profile password
 * Returns:
 * 1. 200 - Returns the true if the password is changed successfully
 * 2. 607 - Weak password
 * 3. 613 - Entered old password does not match the existing password
 * 4. 614 - New password should not same as old password
 * 5. 500 - Other errors
 */
export async function changeStykuPasswordAsync(
  request: Request,
  payload: Password,
) {
  const authToken: string | null = request.headers.get(
    HEADERS.X_STYKU_AUTHORIZATION,
  );
  const response: APIResponse<Save> = await changePassword(payload, authToken);
  return response;
}
