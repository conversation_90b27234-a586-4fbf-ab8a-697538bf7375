import type { ShopifyMetaobjectUpsertInput } from "../shopify/metaobject";

export default class BusinessType {
  id: number = 0;
  name: string = "";
  nameConstant: string = "";

  static toShopifyMetaobject(obj: BusinessType): ShopifyMetaobjectUpsertInput {
    return {
      fields: [
        {
          key: "business_id",
          value: obj.id.toString(),
        },
        {
          key: "business_name",
          value: obj.name,
        },
        {
          key: "business_name_constant",
          value: obj.nameConstant,
        },
      ],
    };
  }
}
