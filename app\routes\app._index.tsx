import type { LoaderFunctionArgs } from "@remix-run/node";
import { Layout, Page } from "@shopify/polaris";
import styles from "./_index/styles.module.css";
import { authenticate } from "../shopify.server";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  await authenticate.admin(request);

  return null;
};

export default function Index() {
  return (
    <Page fullWidth>
      <Layout>
        <div className={styles.layout}>
          <img
            src="https://cdn.shopify.com/s/files/1/0575/7678/3970/files/styku-logo.png?v=**********"
            alt="Styku logo"
            loading="eager"
            className={styles.logo}
          ></img>
          <h2 className={styles.title}>Welcome to Health Pass</h2>
        </div>
      </Layout>
    </Page>
  );
}
