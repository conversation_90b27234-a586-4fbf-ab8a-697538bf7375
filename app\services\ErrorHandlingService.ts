import type { AdminApiContextWithoutRest } from "node_modules/@shopify/shopify-app-remix/dist/ts/server/clients";
import type {
    ErrorHandlePayload,
    ErrorHandlingParams,
} from "~/models/errorHandling";
import { ErrorLogMetaobject } from "~/models/errorHandling";
import { createMetaobject } from "~/api/shopify/graphQL/metaObject";
import { ERROR_META_DEFINITION, OWNER_TYPE } from "~/utils/shopifyConstants";
import type { MetafieldInput } from "~/models/shopify/metafield";
import { graphQLUtils } from "~/api/shopify/graphQL/utility";
import { ERROR_MESSAGES } from "~/utils/stringConstants";
import { updateMetafield } from "~/api/shopify/graphQL/metafields";
import { logErrorToConsole } from "./loggingService";
import type { OrderResponse } from "~/models/shopify/webhook/order";
import { ErrorSource } from "~/models/enums";

export class ErrorHandlingService {
    static async handleError({
        admin,
        context,
        error,
        isCritical = false,
        params,
    }: {
        admin: AdminApiContextWithoutRest;
        context: string;
        error: Error;
        isCritical: boolean;
        params?: ErrorHandlingParams;
    }) {
        // Log Error to the console
        logErrorToConsole(context, error);

        // Create Error payload to create metaobject
        const errorPayload = this.createErrorPayload(error, context, isCritical);
        const metaObjectId = await this.createErrorMetaobject(admin, errorPayload);

        // Reference the created metaobject with the metafield based on the parameter values, such as: (Order, Customer)
        if (params && metaObjectId) {
            await this.updateShopifyMetafield(admin, metaObjectId, params);
        }
    }

    private static createErrorPayload(
        error: Error,
        context: string,
        isCritical: boolean,
    ): ErrorHandlePayload {
        return {
            context,
            error: { error },
            isCritical,
            createdAt: new Date(),
            updatedAt: new Date(),
        };
    }

    private static async createErrorMetaobject(
        admin: AdminApiContextWithoutRest,
        errorPayload: ErrorHandlePayload,
    ): Promise<string | undefined> {
        const metaobjectInput = ErrorLogMetaobject.create(errorPayload);

        try {
            return await createMetaobject(
                admin,
                ERROR_META_DEFINITION.type,
                metaobjectInput,
            );
        } catch (error) {
            logErrorToConsole(
                ERROR_MESSAGES.SHOPIFY.CREATE_METAOBJECT,
                error as Error,
            );
        }
    }

    private static async updateShopifyMetafield(
        admin: AdminApiContextWithoutRest,
        metaObjectId: string,
        params: {
            ownerType: string;
            ownerId: number;
            namespace: string;
            key: string;
            type: string;
        },
    ): Promise<void> {
        const { ownerId, ownerType, namespace, key, type } = params;

        const metafieldData: MetafieldInput = {
            ownerId: graphQLUtils.createGraphQLId(ownerType, ownerId),
            namespace,
            key,
            type,
            value: JSON.stringify([metaObjectId]),
        };

        const updatedMetafield = await updateMetafield(admin, metafieldData);

        if (!updatedMetafield) {
            logErrorToConsole(ERROR_MESSAGES.SHOPIFY.UPDATE_METAFIELD);
        }
    }

    // Helper function to determine the owner type and ID based on the error context
    static getOwnerDetails(
        context: string | undefined,
        payload: OrderResponse,
    ): { ownerType: string; ownerId: number } {
        let ownerType = "";
        let ownerId = 0;

        if (!context) {
            return { ownerType, ownerId };
        }

        switch (context) {
            case ErrorSource.Customer:
                ownerType = OWNER_TYPE.CUSTOMER;
                ownerId = payload.customer.id;
                break;
            case ErrorSource.Testkit:
            case ErrorSource.User:
            case ErrorSource.Order:
                ownerType = OWNER_TYPE.ORDER;
                ownerId = payload.id;
                break;
            default:
                break;
        }

        return { ownerType, ownerId };
    }
}
