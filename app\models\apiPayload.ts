import { HEADERS } from "~/utils/apiConstants";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "~/utils/stringConstants";

export default class APIPayload<TBody> {
  endpoint: string;
  method: string;
  headers: Headers;
  successMessage: string = SUCCESS_MESSAGES.SUCCESS;
  failureMessage: string = ERROR_MESSAGES.FAILURE;
  body: TBody | null = null;

  constructor(endpoint: string, method: string) {
    this.endpoint = endpoint;
    this.method = method;
    this.headers = new Headers();
    this.headers.set(HEADERS.CONTENT_TYPE, HEADERS.CONTENT_APPLICATION_JSON);
  }

  addAuthHeader(authToken: string | null): void {
    authToken && this.headers.set(HEADERS.AUTHORIZATION, authToken);
  }

  addShopifyAuthHeader(accessToken: string | null): void {
    accessToken && this.headers.set(HEADERS.X_SHOPIFY_ACCESS_TOKEN, accessToken);
  }
}
